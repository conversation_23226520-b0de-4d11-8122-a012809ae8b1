'use client'

import { Button } from '@/components/ui/button'
import { Twitter, Linkedin, Facebook, Copy, Check } from 'lucide-react'
import { useState } from 'react'

interface SocialShareButtonsProps {
  title: string
  url: string
  className?: string
}

export function SocialShareButtons({ title, url, className = '' }: SocialShareButtonsProps) {
  const [copied, setCopied] = useState(false)

  const shareUrls = {
    twitter: `https://twitter.com/intent/tweet?text=${encodeURIComponent(title)}&url=${encodeURIComponent(url)}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`,
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`,
  }

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(url)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy link:', err)
    }
  }

  const openShareWindow = (shareUrl: string) => {
    window.open(
      shareUrl,
      'share',
      'width=600,height=400,scrollbars=yes,resizable=yes'
    )
  }

  return (
    <div className={`flex items-center gap-1 ${className}`}>
      <Button
        variant="ghost"
        size="sm"
        className="h-10 w-10 p-0 rounded-full hover:bg-muted transition-colors"
        onClick={() => openShareWindow(shareUrls.twitter)}
        title="Share on Twitter"
      >
        <Twitter className="h-5 w-5 text-muted-foreground hover:text-foreground" />
      </Button>

      <Button
        variant="ghost"
        size="sm"
        className="h-10 w-10 p-0 rounded-full hover:bg-muted transition-colors"
        onClick={() => openShareWindow(shareUrls.linkedin)}
        title="Share on LinkedIn"
      >
        <Linkedin className="h-5 w-5 text-muted-foreground hover:text-foreground" />
      </Button>

      <Button
        variant="ghost"
        size="sm"
        className="h-10 w-10 p-0 rounded-full hover:bg-muted transition-colors"
        onClick={() => openShareWindow(shareUrls.facebook)}
        title="Share on Facebook"
      >
        <Facebook className="h-5 w-5 text-muted-foreground hover:text-foreground" />
      </Button>

      <Button
        variant="ghost"
        size="sm"
        className="h-10 w-10 p-0 rounded-full hover:bg-muted transition-colors"
        onClick={handleCopyLink}
        title={copied ? "Link copied!" : "Copy link"}
      >
        {copied ? (
          <Check className="h-5 w-5 text-green-600" />
        ) : (
          <Copy className="h-5 w-5 text-muted-foreground hover:text-foreground" />
        )}
      </Button>
    </div>
  )
}
