'use client'

import { But<PERSON> } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import { useRouter } from 'next/navigation'

interface BackButtonProps {
  href?: string
  className?: string
}

export function BackButton({ href = '/blog', className = '' }: BackButtonProps) {
  const router = useRouter()

  const handleBack = () => {
    // Try to go back in history first, fallback to href
    if (window.history.length > 1) {
      router.back()
    } else {
      router.push(href)
    }
  }

  return (
    <Button 
      variant="ghost" 
      className={`gap-2 hover:bg-muted ${className}`}
      onClick={handleBack}
    >
      <ArrowLeft className="h-4 w-4" />
      Back to Blog
    </Button>
  )
}
