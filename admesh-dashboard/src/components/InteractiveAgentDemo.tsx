"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { motion } from "framer-motion";
import {
  Bot,
  MessageSquare,
  Mic,
  Star,
  Building,
  Sparkles,
  User,
  Volume2,
  Headphones,
  Phone,
  Send,
  Clock,
  MoreHorizontal
} from "lucide-react";

interface AgentScenario {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  preview: React.ReactNode;
}

export default function InteractiveAgentDemo() {
  const [selectedScenario, setSelectedScenario] = useState<string>("research");

  const agentScenarios: AgentScenario[] = [
    {
      id: "research",
      name: "AI Research Assistant",
      description: "Product research and comparison agents",
      icon: <MessageSquare className="w-5 h-5" />,
      preview: (
        <div className="bg-white border rounded-lg p-3 max-w-sm mx-auto shadow-sm">
          {/* Research Assistant Header */}
          <div className="flex items-center gap-2 pb-2 border-b">
            <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
              <Bot className="w-3 h-3 text-purple-600" />
            </div>
            <div className="flex-1">
              <span className="font-medium text-gray-900 text-sm">Research Assistant</span>
              <div className="flex items-center gap-1">
                <div className="w-1 h-1 bg-green-500 rounded-full"></div>
                <span className="text-xs text-gray-500">Finding the best options for you</span>
              </div>
            </div>
            <MoreHorizontal className="w-4 h-4 text-gray-400" />
          </div>

          {/* Chat Messages */}
          <div className="space-y-2 py-2">
            <div className="flex gap-2">
              <User className="w-4 h-4 text-gray-400 mt-1" />
              <div className="bg-gray-100 rounded-lg p-2 text-xs max-w-[80%]">
                I need to find the best project management tool for my startup team of 8 people
              </div>
            </div>

            <div className="flex gap-2">
              <Bot className="w-4 h-4 text-purple-600 mt-1" />
              <div className="bg-purple-50 rounded-lg p-2 text-xs max-w-[80%]">
                Based on your team size and startup needs, I found some excellent options. Notion stands out for its flexibility and collaboration features.

                {/* Integrated Recommendation */}
                <div className="mt-2 bg-white border rounded p-2 shadow-sm">
                  <div className="flex items-start gap-2">
                    <div className="w-6 h-6 bg-gray-100 rounded flex items-center justify-center">
                      <Building className="w-3 h-3 text-gray-600" />
                    </div>
                    <div className="flex-1">
                      <h5 className="font-medium text-gray-900 text-xs">Notion</h5>
                      <p className="text-gray-600 text-xs">All-in-one workspace for teams</p>
                      <div className="flex items-center gap-1 mt-1">
                        <Star className="w-2 h-2 fill-yellow-400 text-yellow-400" />
                        <span className="text-xs text-gray-600">4.7</span>
                        <span className="text-xs text-gray-500">•</span>
                        <span className="text-xs text-gray-600">Free for personal use</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-2 text-xs text-gray-600">
                  Would you like me to compare it with other options like Asana or Monday.com?
                </div>
              </div>
            </div>
          </div>

          {/* Chat Input */}
          <div className="flex items-center gap-2 pt-2 border-t">
            <input className="flex-1 text-xs bg-gray-50 rounded px-2 py-1" placeholder="What would you like to research?" />
            <Send className="w-3 h-3 text-gray-400" />
          </div>

          {/* AdMesh Attribution */}
          <div className="mt-2 text-xs text-gray-400 flex items-center gap-1">
            <Sparkles className="w-2 h-2" />
            Powered by AdMesh
          </div>
        </div>
      )
    },
    {
      id: "personal",
      name: "Personal AI Assistant",
      description: "Productivity and personal task management",
      icon: <Bot className="w-5 h-5" />,
      preview: (
        <div className="bg-white border rounded-lg p-3 max-w-sm mx-auto shadow-sm">
          {/* Personal Assistant Header */}
          <div className="flex items-center gap-2 pb-2 border-b">
            <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
              <Bot className="w-3 h-3 text-purple-600" />
            </div>
            <div className="flex-1">
              <span className="font-medium text-gray-900 text-sm">Alex AI</span>
              <div className="flex items-center gap-1">
                <div className="w-1 h-1 bg-green-500 rounded-full"></div>
                <span className="text-xs text-gray-500">Your personal assistant</span>
              </div>
            </div>
            <Clock className="w-4 h-4 text-gray-400" />
          </div>

          {/* Conversation */}
          <div className="space-y-2 py-2">
            <div className="flex gap-2">
              <User className="w-4 h-4 text-gray-400 mt-1" />
              <div className="bg-gray-100 rounded-lg p-2 text-xs max-w-[80%]">
                I need to set up a project management system for my team. Any suggestions?
              </div>
            </div>

            <div className="flex gap-2">
              <Bot className="w-4 h-4 text-purple-600 mt-1" />
              <div className="bg-purple-50 rounded-lg p-2 text-xs max-w-[80%]">
                Based on your team size and workflow, I recommend Notion. It&apos;s perfect for project management and team collaboration.

                {/* Integrated Recommendation */}
                <div className="mt-2 bg-white border rounded p-2 shadow-sm">
                  <div className="flex items-start gap-2">
                    <div className="w-6 h-6 bg-gray-100 rounded flex items-center justify-center">
                      <Building className="w-3 h-3 text-gray-600" />
                    </div>
                    <div className="flex-1">
                      <h5 className="font-medium text-gray-900 text-xs">Notion</h5>
                      <p className="text-gray-600 text-xs">All-in-one workspace for teams</p>
                      <div className="flex items-center gap-1 mt-1">
                        <Star className="w-2 h-2 fill-yellow-400 text-yellow-400" />
                        <span className="text-xs text-gray-600">4.7</span>
                        <span className="text-xs text-gray-500">•</span>
                        <span className="text-xs text-gray-600">Free for personal use</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-2 text-xs text-gray-600">
                  I can help you set up your workspace if you&apos;d like!
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="flex items-center gap-1 pt-2 border-t">
            <div className="flex-1 text-xs bg-gray-50 rounded px-2 py-1 text-gray-500">Ask me anything...</div>
            <Mic className="w-3 h-3 text-gray-400" />
            <Send className="w-3 h-3 text-gray-400" />
          </div>

          {/* AdMesh Attribution */}
          <div className="mt-2 text-xs text-gray-400 flex items-center gap-1">
            <Sparkles className="w-2 h-2" />
            Powered by AdMesh
          </div>
        </div>
      )
    },
    {
      id: "voice",
      name: "Voice Assistant Interface",
      description: "Audio recommendations with visual presentation",
      icon: <Headphones className="w-5 h-5" />,
      preview: (
        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 border rounded-lg p-3 max-w-sm mx-auto shadow-sm">
          {/* Voice Interface Header */}
          <div className="flex items-center gap-2 pb-2 border-b border-blue-200">
            <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
              <Volume2 className="w-3 h-3 text-blue-600" />
            </div>
            <div className="flex-1">
              <span className="font-medium text-gray-900 text-sm">Voice Assistant</span>
              <div className="flex items-center gap-1">
                <div className="w-1 h-1 bg-blue-500 rounded-full animate-pulse"></div>
                <span className="text-xs text-blue-600">Listening...</span>
              </div>
            </div>
            <Phone className="w-4 h-4 text-blue-400" />
          </div>

          {/* Voice Interaction */}
          <div className="space-y-2 py-2">
            <div className="bg-white/70 border border-blue-200 rounded-lg p-2 text-xs">
              <div className="flex items-center gap-1 mb-1">
                <User className="w-3 h-3 text-blue-600" />
                <span className="font-medium text-blue-700">You said:</span>
              </div>
              <p className="text-gray-700">&quot;What&apos;s the best CRM for small businesses?&quot;</p>
            </div>

            <div className="bg-blue-100/70 border border-blue-200 rounded-lg p-2 text-xs">
              <div className="flex items-center gap-1 mb-1">
                <Volume2 className="w-3 h-3 text-blue-600" />
                <span className="font-medium text-blue-700">Assistant:</span>
              </div>
              <p className="text-gray-700 mb-2">
                &quot;I recommend HubSpot CRM for small businesses. It&apos;s free to start and offers excellent features for growing teams.&quot;
              </p>

              {/* Visual Recommendation Card */}
              <div className="bg-white border border-blue-200 rounded p-2 shadow-sm">
                <div className="flex items-center gap-2">
                  <div className="w-6 h-6 bg-orange-100 rounded flex items-center justify-center">
                    <Building className="w-3 h-3 text-orange-600" />
                  </div>
                  <div className="flex-1">
                    <h5 className="font-medium text-gray-900 text-xs">HubSpot CRM</h5>
                    <p className="text-gray-600 text-xs">Free CRM for growing businesses</p>
                    <div className="flex items-center gap-1 mt-1">
                      <Star className="w-2 h-2 fill-yellow-400 text-yellow-400" />
                      <span className="text-xs text-gray-600">4.5</span>
                      <Badge variant="secondary" className="text-xs px-1 py-0">Free</Badge>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Voice Controls */}
          <div className="flex items-center justify-center gap-3 pt-2 border-t border-blue-200">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
              <Mic className="w-4 h-4 text-white" />
            </div>
            <div className="text-xs text-blue-600">Tap to speak</div>
          </div>

          {/* AdMesh Attribution */}
          <div className="mt-2 text-xs text-gray-400 flex items-center gap-1">
            <Sparkles className="w-2 h-2" />
            Powered by AdMesh
          </div>
        </div>
      )
    }
  ];

  const selectedScenarioData = agentScenarios.find(scenario => scenario.id === selectedScenario);

  return (
    <div className="w-full max-w-lg mx-auto px-4 sm:px-0">
      <Card className="border border-gray-200 shadow-lg">
        <CardHeader className="text-center pb-4">
          <CardTitle className="flex items-center justify-center gap-2 text-lg">
            <Bot className="w-5 h-5 text-purple-600" />
            Seamless Agent Integration
          </CardTitle>
          <p className="text-gray-600 text-sm">
            See how AdMesh integrates naturally without compromising your UI
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Scenario Selector */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Choose Agent Type</label>
            <Select value={selectedScenario} onValueChange={setSelectedScenario}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select an agent type" />
              </SelectTrigger>
              <SelectContent>
                {agentScenarios.map((scenario) => (
                  <SelectItem key={scenario.id} value={scenario.id}>
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 flex items-center justify-center">
                        {scenario.icon}
                      </div>
                      <div>
                        <span className="font-medium">{scenario.name}</span>
                        <span className="text-xs text-gray-500 ml-2">- {scenario.description}</span>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Scenario Preview */}
          {selectedScenarioData && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-3"
            >
              <div className="text-center">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Live Preview</h4>
                {selectedScenarioData.preview}
              </div>

              {/* Developer Freedom Note */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="text-center space-y-2">
                  <p className="text-blue-700 text-xs font-medium">
                    Explore more ad formats at{" "}
                    <a
                      href="https://storybook.useadmesh.com"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="underline hover:text-blue-800"
                    >
                      storybook.useadmesh.com
                    </a>
                  </p>
                  <p className="text-blue-600 text-xs">
                    <strong>Developer Freedom:</strong> You&apos;re not limited to our formats! Use any ad format that works for your users and converts well.
                  </p>
                </div>
              </div>
            </motion.div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
