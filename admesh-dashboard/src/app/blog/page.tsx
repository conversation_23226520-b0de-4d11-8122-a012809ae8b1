import { client, blogPostsQuery, featuredPostsQuery, BlogPost, urlFor, getCategoryColor } from '@/lib/sanity'
import Link from 'next/link'
import { format } from 'date-fns'
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { BlogImage } from '@/components/blog/BlogImage'
import { CalendarDays, User, ArrowRight, Clock, Star } from 'lucide-react'

async function getBlogPosts(): Promise<BlogPost[]> {
  try {
    const posts = await client.fetch(blogPostsQuery)
    return posts || []
  } catch (error) {
    console.error('Error fetching blog posts:', error)
    return []
  }
}

async function getFeaturedPosts(): Promise<BlogPost[]> {
  try {
    const posts = await client.fetch(featuredPostsQuery)
    return posts || []
  } catch (error) {
    console.error('Error fetching featured posts:', error)
    return []
  }
}

export default async function BlogPage() {
  const [posts, featuredPosts] = await Promise.all([
    getBlogPosts(),
    getFeaturedPosts()
  ])

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-16 max-w-6xl">
        {/* Header */}
        <div className="text-center mb-24">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-foreground mb-10 tracking-tight">
            AdMesh Blog
          </h1>
          <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Insights, updates, and stories from the AdMesh ecosystem. Discover the latest in AI marketing,
            performance optimization, and industry trends.
          </p>
        </div>

        {/* Featured Posts - Medium Style */}
        {featuredPosts.length > 0 && (
          <div className="mb-16">
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-foreground">Featured</h2>
            </div>
            <div className="space-y-8">
              {featuredPosts.map((post, index) => (
                <article key={post._id} className={`group ${index < featuredPosts.length - 1 ? 'pb-8 border-b border-border' : ''}`}>
                  <Link href={`/blog/${post.slug.current}`} className="block">
                    <div className={`flex gap-6 ${index === 0 ? 'flex-col md:flex-row' : 'flex-col sm:flex-row'}`}>
                      <div className={`flex-1 ${index === 0 ? 'order-2 md:order-1' : ''}`}>
                        <div className="space-y-3">
                          {post.author && (
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <span className="font-medium">{post.author.name}</span>
                              <span>·</span>
                              <span>{format(new Date(post.publishedAt), 'MMM dd')}</span>
                            </div>
                          )}
                          <h3 className={`font-bold text-foreground group-hover:text-primary transition-colors line-clamp-2 leading-tight ${index === 0 ? 'text-2xl md:text-3xl' : 'text-xl'}`}>
                            {post.title}
                          </h3>
                          {post.excerpt && (
                            <p className={`text-muted-foreground line-clamp-3 leading-relaxed ${index === 0 ? 'text-lg' : 'text-base'}`}>
                              {post.excerpt}
                            </p>
                          )}
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            {post.categories && post.categories.length > 0 && (
                              <span className="bg-muted px-2 py-1 rounded text-xs">
                                {post.categories[0].title}
                              </span>
                            )}
                            <Badge variant="secondary" className="text-xs">
                              <Star className="h-3 w-3 mr-1" />
                              Featured
                            </Badge>
                          </div>
                        </div>
                      </div>
                      {post.mainImage && (
                        <div className={`flex-shrink-0 ${index === 0 ? 'order-1 md:order-2 w-full md:w-80' : 'w-32 md:w-40'}`}>
                          <div className={`relative overflow-hidden rounded-lg bg-muted ${index === 0 ? 'aspect-[16/10]' : 'aspect-square'}`}>
                            <BlogImage
                              src={urlFor(post.mainImage).width(index === 0 ? 320 : 160).height(index === 0 ? 200 : 160).url()}
                              alt={post.mainImage.alt || post.title}
                              fill
                              className="object-cover group-hover:scale-105 transition-transform duration-300"
                              sizes={index === 0 ? "(max-width: 768px) 100vw, 320px" : "160px"}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </Link>
                </article>
              ))}
            </div>
          </div>
        )}
                  <CardContent className="p-7">
                    <div className="space-y-5 h-full flex flex-col">
                      {/* Categories */}
                      {post.categories && post.categories.length > 0 && (
                        <div className="flex flex-wrap gap-2">
                          {post.categories.slice(0, 2).map((category) => (
                            <Badge
                              key={category._id}
                              variant="secondary"
                              className={`${getCategoryColor(category.color)} px-3 py-1`}
                            >
                              {category.title}
                            </Badge>
                          ))}
                        </div>
                      )}

                      {/* Title */}
                      <h3 className="text-xl font-semibold text-foreground group-hover:text-primary transition-colors line-clamp-2 leading-tight">
                        {post.title}
                      </h3>

                      {/* Excerpt */}
                      {post.excerpt && (
                        <p className="text-muted-foreground line-clamp-3 leading-relaxed flex-grow">
                          {post.excerpt}
                        </p>
                      )}

                      {/* Meta Information */}
                      <div className="flex items-center justify-between text-sm text-muted-foreground pt-4 border-t border-border mt-auto">
                        <div className="flex items-center gap-4">
                          {post.author && (
                            <div className="flex items-center gap-2">
                              {post.author.image && (
                                <Avatar className="h-6 w-6">
                                  <AvatarImage
                                    src={urlFor(post.author.image).width(24).height(24).url()}
                                    alt={post.author.name}
                                  />
                                  <AvatarFallback className="text-xs">
                                    {post.author.name.charAt(0)}
                                  </AvatarFallback>
                                </Avatar>
                              )}
                              <span className="font-medium">{post.author.name}</span>
                            </div>
                          )}
                          <div className="flex items-center gap-1">
                            <CalendarDays className="h-4 w-4" />
                            <span>{format(new Date(post.publishedAt), 'MMM dd')}</span>
                          </div>
                        </div>
                        {(post.readingTime || post.estimatedReadingTime) && (
                          <div className="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            <span>{post.readingTime || post.estimatedReadingTime} min</span>
                          </div>
                        )}
                      </div>

                      {/* Read More Link */}
                      <Link
                        href={`/blog/${post.slug.current}`}
                        className="inline-flex items-center gap-2 text-primary hover:text-primary/80 font-medium transition-colors pt-2 relative z-20"
                      >
                        Read more
                        <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                      </Link>
                    </div>
                  </CardContent>

                  {/* Clickable overlay */}
                  <Link
                    href={`/blog/${post.slug.current}`}
                    className="absolute inset-0 z-10"
                    aria-label={`Read ${post.title}`}
                  />
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* All Blog Posts - Medium Style */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-foreground">
            {featuredPosts.length > 0 ? 'Latest' : 'All Posts'}
          </h2>
        </div>

        {posts.length > 0 ? (
          <div className="space-y-8">
            {posts.filter(post => !post.featured).map((post) => (
              <article key={post._id} className="group pb-8 border-b border-border last:border-b-0">
                <Link href={`/blog/${post.slug.current}`} className="block">
                  <div className="flex gap-6 flex-col sm:flex-row">
                    <div className="flex-1 order-2 sm:order-1">
                      <div className="space-y-3">
                        {post.author && (
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <span className="font-medium">{post.author.name}</span>
                            <span>·</span>
                            <span>{format(new Date(post.publishedAt), 'MMM dd')}</span>
                            {(post.readingTime || post.estimatedReadingTime) && (
                              <>
                                <span>·</span>
                                <span>{post.readingTime || post.estimatedReadingTime} min read</span>
                              </>
                            )}
                          </div>
                        )}
                        <h3 className="text-xl font-bold text-foreground group-hover:text-primary transition-colors line-clamp-2 leading-tight">
                          {post.title}
                        </h3>
                        {post.excerpt && (
                          <p className="text-muted-foreground line-clamp-3 leading-relaxed text-base">
                            {post.excerpt}
                          </p>
                        )}
                        <div className="flex items-center gap-3 text-sm text-muted-foreground">
                          {post.categories && post.categories.length > 0 && (
                            <span className="bg-muted px-2 py-1 rounded text-xs">
                              {post.categories[0].title}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    {post.mainImage && (
                      <div className="flex-shrink-0 w-full sm:w-32 md:w-40 order-1 sm:order-2">
                        <div className="relative aspect-[16/10] sm:aspect-square overflow-hidden rounded-lg bg-muted">
                          <BlogImage
                            src={urlFor(post.mainImage).width(160).height(160).url()}
                            alt={post.mainImage.alt || post.title}
                            fill
                            className="object-cover group-hover:scale-105 transition-transform duration-300"
                            sizes="160px"
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </Link>
              </article>
            ))}
          </div>
        ) : (
          <div className="text-center py-20">
            <div className="max-w-lg mx-auto">
              <div className="w-24 h-24 mx-auto mb-6 rounded-full bg-muted flex items-center justify-center">
                <User className="h-12 w-12 text-muted-foreground" />
              </div>
              <h3 className="text-3xl font-semibold text-foreground mb-4">
                No blog posts yet
              </h3>
              <p className="text-lg text-muted-foreground mb-8 leading-relaxed">
                We&apos;re working on creating amazing content about AI marketing, performance optimization,
                and industry insights. Check back soon for the latest updates!
              </p>
              <Badge variant="secondary" className="px-6 py-3 text-sm">
                Coming Soon
              </Badge>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

// Generate metadata for SEO
export async function generateMetadata() {
  return {
    title: 'Blog | AdMesh - AI Marketing Insights & Updates',
    description: 'Discover the latest insights, updates, and stories from the AdMesh ecosystem. Learn about AI marketing, performance optimization, and industry trends.',
    keywords: ['AI marketing', 'performance marketing', 'AdMesh blog', 'marketing insights', 'AI agents'],
    openGraph: {
      title: 'Blog | AdMesh - AI Marketing Insights & Updates',
      description: 'Discover the latest insights, updates, and stories from the AdMesh ecosystem. Learn about AI marketing, performance optimization, and industry trends.',
      type: 'website',
      siteName: 'AdMesh',
    },
    twitter: {
      card: 'summary_large_image',
      title: 'Blog | AdMesh - AI Marketing Insights & Updates',
      description: 'Discover the latest insights, updates, and stories from the AdMesh ecosystem.',
    },
  }
}
