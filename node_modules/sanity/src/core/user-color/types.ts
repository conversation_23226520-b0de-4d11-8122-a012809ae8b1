import {type ColorHue<PERSON>ey, type ColorTints} from '@sanity/color'
import {type Observable} from 'rxjs'

/** @internal */
export type HexColor = string

/** @internal */
export type UserColorHue = string

/** @internal */
export type UserId = string

/** @internal */
export interface UserColor {
  name: <PERSON>Hue<PERSON>ey
  background: HexColor
  border: HexColor
  text: HexColor
  tints: ColorTints
}

/** @internal */
export interface UserColorManager {
  get: (userId: UserId | null) => UserColor
  listen: (userId: UserId) => Observable<UserColor>
}
