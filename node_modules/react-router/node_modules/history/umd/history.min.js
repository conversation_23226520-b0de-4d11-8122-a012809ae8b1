!function(n,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t(n.History={})}(this,function(n){"use strict";function E(){return(E=Object.assign||function(n){for(var t=1;t<arguments.length;t++){var e=arguments[t];for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(n[o]=e[o])}return n}).apply(this,arguments)}function d(n){return"/"===n.charAt(0)}function v(n,t){for(var e=t,o=e+1,r=n.length;o<r;e+=1,o+=1)n[e]=n[o];n.pop()}function i(n){return n.valueOf?n.valueOf():Object.prototype.valueOf.call(n)}function H(n){return"/"===n.charAt(0)?n:"/"+n}function t(n){return"/"===n.charAt(0)?n.substr(1):n}function S(n,t){return function(n,t){return 0===n.toLowerCase().indexOf(t.toLowerCase())&&-1!=="/?#".indexOf(n.charAt(t.length))}(n,t)?n.substr(t.length):n}function U(n){return"/"===n.charAt(n.length-1)?n.slice(0,-1):n}function a(n){var t=n||"/",e="",o="",r=t.indexOf("#");-1!==r&&(o=t.substr(r),t=t.substr(0,r));var i=t.indexOf("?");return-1!==i&&(e=t.substr(i),t=t.substr(0,i)),{pathname:t,search:"?"===e?"":e,hash:"#"===o?"":o}}function j(n){var t=n.pathname,e=n.search,o=n.hash,r=t||"/";return e&&"?"!==e&&(r+="?"===e.charAt(0)?e:"?"+e),o&&"#"!==o&&(r+="#"===o.charAt(0)?o:"#"+o),r}function C(n,t,e,o){var r;"string"==typeof n?(r=a(n)).state=t:(void 0===(r=E({},n)).pathname&&(r.pathname=""),r.search?"?"!==r.search.charAt(0)&&(r.search="?"+r.search):r.search="",r.hash?"#"!==r.hash.charAt(0)&&(r.hash="#"+r.hash):r.hash="",void 0!==t&&void 0===r.state&&(r.state=t));try{r.pathname=decodeURI(r.pathname)}catch(n){throw n instanceof URIError?new URIError('Pathname "'+r.pathname+'" could not be decoded. This is likely caused by an invalid percent-encoding.'):n}return e&&(r.key=e),o?r.pathname?"/"!==r.pathname.charAt(0)&&(r.pathname=function(n,t){void 0===t&&(t="");var e,o=n&&n.split("/")||[],r=t&&t.split("/")||[],i=n&&d(n),a=t&&d(t),c=i||a;if(n&&d(n)?r=o:o.length&&(r.pop(),r=r.concat(o)),!r.length)return"/";if(r.length){var u=r[r.length-1];e="."===u||".."===u||""===u}else e=!1;for(var f=0,s=r.length;0<=s;s--){var h=r[s];"."===h?v(r,s):".."===h?(v(r,s),f++):f&&(v(r,s),f--)}if(!c)for(;f--;)r.unshift("..");!c||""===r[0]||r[0]&&d(r[0])||r.unshift("");var l=r.join("/");return e&&"/"!==l.substr(-1)&&(l+="/"),l}(r.pathname,o.pathname)):r.pathname=o.pathname:r.pathname||(r.pathname="/"),r}function I(){var i=null;var o=[];return{setPrompt:function(n){return i=n,function(){i===n&&(i=null)}},confirmTransitionTo:function(n,t,e,o){if(null!=i){var r="function"==typeof i?i(n,t):i;"string"==typeof r?"function"==typeof e?e(r,o):o(!0):o(!1!==r)}else o(!0)},appendListener:function(n){var t=!0;function e(){t&&n.apply(void 0,arguments)}return o.push(e),function(){t=!1,o=o.filter(function(n){return n!==e})}},notifyListeners:function(){for(var n=arguments.length,t=new Array(n),e=0;e<n;e++)t[e]=arguments[e];o.forEach(function(n){return n.apply(void 0,t)})}}}var M=!("undefined"==typeof window||!window.document||!window.document.createElement);function R(n,t){t(window.confirm(n))}var e="Invariant failed";function B(n){if(!n)throw new Error(e)}var F="popstate",q="hashchange";function _(){try{return window.history.state||{}}catch(n){return{}}}var T="hashchange",L={hashbang:{encodePath:function(n){return"!"===n.charAt(0)?n:"!/"+t(n)},decodePath:function(n){return"!"===n.charAt(0)?n.substr(1):n}},noslash:{encodePath:t,decodePath:H},slash:{encodePath:H,decodePath:H}};function G(n){var t=n.indexOf("#");return-1===t?n:n.slice(0,t)}function W(){var n=window.location.href,t=n.indexOf("#");return-1===t?"":n.substring(t+1)}function z(n){window.location.replace(G(window.location.href)+"#"+n)}function g(n,t,e){return Math.min(Math.max(n,t),e)}n.createBrowserHistory=function(n){void 0===n&&(n={}),M||B(!1);var c=window.history,u=function(){var n=window.navigator.userAgent;return(-1===n.indexOf("Android 2.")&&-1===n.indexOf("Android 4.0")||-1===n.indexOf("Mobile Safari")||-1!==n.indexOf("Chrome")||-1!==n.indexOf("Windows Phone"))&&(window.history&&"pushState"in window.history)}(),t=!(-1===window.navigator.userAgent.indexOf("Trident")),e=n,o=e.forceRefresh,f=void 0!==o&&o,r=e.getUserConfirmation,s=void 0===r?R:r,i=e.keyLength,a=void 0===i?6:i,h=n.basename?U(H(n.basename)):"";function l(n){var t=n||{},e=t.key,o=t.state,r=window.location,i=r.pathname+r.search+r.hash;return h&&(i=S(i,h)),C(i,o,e)}function d(){return Math.random().toString(36).substr(2,a)}var v=I();function p(n){E(L,n),L.length=c.length,v.notifyListeners(L.location,L.action)}function w(n){!function(n){return void 0===n.state&&-1===navigator.userAgent.indexOf("CriOS")}(n)&&m(l(n.state))}function g(){m(l(_()))}var y=!1;function m(t){if(y)y=!1,p();else{v.confirmTransitionTo(t,"POP",s,function(n){n?p({action:"POP",location:t}):function(n){var t=L.location,e=O.indexOf(t.key);-1===e&&(e=0);var o=O.indexOf(n.key);-1===o&&(o=0);var r=e-o;r&&(y=!0,b(r))}(t)})}}var P=l(_()),O=[P.key];function x(n){return h+j(n)}function b(n){c.go(n)}var A=0;function k(n){1===(A+=n)&&1===n?(window.addEventListener(F,w),t&&window.addEventListener(q,g)):0===A&&(window.removeEventListener(F,w),t&&window.removeEventListener(q,g))}var T=!1,L={length:c.length,action:"POP",location:P,createHref:x,push:function(n,t){var a=C(n,t,d(),L.location);v.confirmTransitionTo(a,"PUSH",s,function(n){if(n){var t=x(a),e=a.key,o=a.state;if(u)if(c.pushState({key:e,state:o},null,t),f)window.location.href=t;else{var r=O.indexOf(L.location.key),i=O.slice(0,r+1);i.push(a.key),O=i,p({action:"PUSH",location:a})}else window.location.href=t}})},replace:function(n,t){var i="REPLACE",a=C(n,t,d(),L.location);v.confirmTransitionTo(a,i,s,function(n){if(n){var t=x(a),e=a.key,o=a.state;if(u)if(c.replaceState({key:e,state:o},null,t),f)window.location.replace(t);else{var r=O.indexOf(L.location.key);-1!==r&&(O[r]=a.key),p({action:i,location:a})}else window.location.replace(t)}})},go:b,goBack:function(){b(-1)},goForward:function(){b(1)},block:function(n){void 0===n&&(n=!1);var t=v.setPrompt(n);return T||(k(1),T=!0),function(){return T&&(T=!1,k(-1)),t()}},listen:function(n){var t=v.appendListener(n);return k(1),function(){k(-1),t()}}};return L},n.createHashHistory=function(n){void 0===n&&(n={}),M||B(!1);var t=window.history,e=(window.navigator.userAgent.indexOf("Firefox"),n),o=e.getUserConfirmation,a=void 0===o?R:o,r=e.hashType,i=void 0===r?"slash":r,c=n.basename?U(H(n.basename)):"",u=L[i],f=u.encodePath,s=u.decodePath;function h(){var n=s(W());return c&&(n=S(n,c)),C(n)}var l=I();function d(n){E(k,n),k.length=t.length,l.notifyListeners(k.location,k.action)}var v=!1,p=null;function w(){var n=W(),t=f(n);if(n!==t)z(t);else{var e=h(),o=k.location;if(!v&&function(n,t){return n.pathname===t.pathname&&n.search===t.search&&n.hash===t.hash}(o,e))return;if(p===j(e))return;p=null,function(t){if(v)v=!1,d();else{l.confirmTransitionTo(t,"POP",a,function(n){n?d({action:"POP",location:t}):function(n){var t=k.location,e=P.lastIndexOf(j(t));-1===e&&(e=0);var o=P.lastIndexOf(j(n));-1===o&&(o=0);var r=e-o;r&&(v=!0,O(r))}(t)})}}(e)}}var g=W(),y=f(g);g!==y&&z(y);var m=h(),P=[j(m)];function O(n){t.go(n)}var x=0;function b(n){1===(x+=n)&&1===n?window.addEventListener(T,w):0===x&&window.removeEventListener(T,w)}var A=!1,k={length:t.length,action:"POP",location:m,createHref:function(n){var t=document.querySelector("base"),e="";return t&&t.getAttribute("href")&&(e=G(window.location.href)),e+"#"+f(c+j(n))},push:function(n,t){var i=C(n,void 0,void 0,k.location);l.confirmTransitionTo(i,"PUSH",a,function(n){if(n){var t=j(i),e=f(c+t);if(W()!==e){p=t,function(n){window.location.hash=n}(e);var o=P.lastIndexOf(j(k.location)),r=P.slice(0,o+1);r.push(t),P=r,d({action:"PUSH",location:i})}else d()}})},replace:function(n,t){var r="REPLACE",i=C(n,void 0,void 0,k.location);l.confirmTransitionTo(i,r,a,function(n){if(n){var t=j(i),e=f(c+t);W()!==e&&(p=t,z(e));var o=P.indexOf(j(k.location));-1!==o&&(P[o]=t),d({action:r,location:i})}})},go:O,goBack:function(){O(-1)},goForward:function(){O(1)},block:function(n){void 0===n&&(n=!1);var t=l.setPrompt(n);return A||(b(1),A=!0),function(){return A&&(A=!1,b(-1)),t()}},listen:function(n){var t=l.appendListener(n);return b(1),function(){b(-1),t()}}};return k},n.createMemoryHistory=function(n){void 0===n&&(n={});var t=n,r=t.getUserConfirmation,e=t.initialEntries,o=void 0===e?["/"]:e,i=t.initialIndex,a=void 0===i?0:i,c=t.keyLength,u=void 0===c?6:c,f=I();function s(n){E(w,n),w.length=w.entries.length,f.notifyListeners(w.location,w.action)}function h(){return Math.random().toString(36).substr(2,u)}var l=g(a,0,o.length-1),d=o.map(function(n){return C(n,void 0,"string"==typeof n?h():n.key||h())}),v=j;function p(n){var t=g(w.index+n,0,w.entries.length-1),e=w.entries[t];f.confirmTransitionTo(e,"POP",r,function(n){n?s({action:"POP",location:e,index:t}):s()})}var w={length:d.length,action:"POP",location:d[l],index:l,entries:d,createHref:v,push:function(n,t){var o=C(n,t,h(),w.location);f.confirmTransitionTo(o,"PUSH",r,function(n){if(n){var t=w.index+1,e=w.entries.slice(0);e.length>t?e.splice(t,e.length-t,o):e.push(o),s({action:"PUSH",location:o,index:t,entries:e})}})},replace:function(n,t){var e="REPLACE",o=C(n,t,h(),w.location);f.confirmTransitionTo(o,e,r,function(n){n&&(w.entries[w.index]=o,s({action:e,location:o}))})},go:p,goBack:function(){p(-1)},goForward:function(){p(1)},canGo:function(n){var t=w.index+n;return 0<=t&&t<w.entries.length},block:function(n){return void 0===n&&(n=!1),f.setPrompt(n)},listen:function(n){return f.appendListener(n)}};return w},n.createLocation=C,n.locationsAreEqual=function(n,t){return n.pathname===t.pathname&&n.search===t.search&&n.hash===t.hash&&n.key===t.key&&function e(t,o){if(t===o)return!0;if(null==t||null==o)return!1;if(Array.isArray(t))return Array.isArray(o)&&t.length===o.length&&t.every(function(n,t){return e(n,o[t])});if("object"!=typeof t&&"object"!=typeof o)return!1;var n=i(t),r=i(o);return n!==t||r!==o?e(n,r):Object.keys(Object.assign({},t,o)).every(function(n){return e(t[n],o[n])})}(n.state,t.state)},n.parsePath=a,n.createPath=j,Object.defineProperty(n,"__esModule",{value:!0})});
