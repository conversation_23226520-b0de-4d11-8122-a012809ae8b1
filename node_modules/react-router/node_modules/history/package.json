{"name": "history", "version": "4.10.1", "description": "Manage session history with JavaScript", "repository": "ReactTraining/history", "license": "MIT", "author": "<PERSON>", "main": "index.js", "module": "esm/history.js", "unpkg": "umd/history.js", "files": ["DOMUtils.js", "ExecutionEnvironment.js", "LocationUtils.js", "PathUtils.js", "cjs", "createBrowserHistory.js", "createHashHistory.js", "createMemoryHistory.js", "createTransitionManager.js", "es", "esm", "umd", "warnAboutDeprecatedCJSRequire.js"], "sideEffects": false, "scripts": {"build": "rollup -c", "clean": "git clean -fdX .", "lint": "eslint modules", "prepublishOnly": "yarn build", "test": "karma start --single-run"}, "dependencies": {"@babel/runtime": "^7.1.2", "loose-envify": "^1.2.0", "resolve-pathname": "^3.0.0", "tiny-invariant": "^1.0.2", "tiny-warning": "^1.0.0", "value-equal": "^1.0.1"}, "devDependencies": {"@babel/core": "^7.1.2", "@babel/plugin-transform-object-assign": "^7.0.0", "@babel/plugin-transform-runtime": "^7.1.0", "@babel/preset-env": "^7.1.0", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "^7.0.0", "babel-loader": "^8.0.4", "babel-plugin-dev-expression": "^0.2.1", "eslint": "^3.3.0", "eslint-plugin-import": "^2.0.0", "expect": "^21.0.0", "jest-mock": "^21.0.0", "karma": "^3.1.3", "karma-browserstack-launcher": "^1.3.0", "karma-chrome-launcher": "^2.2.0", "karma-firefox-launcher": "^1.1.0", "karma-mocha": "^1.3.0", "karma-mocha-reporter": "^2.2.5", "karma-sourcemap-loader": "^0.3.7", "karma-webpack": "^3.0.5", "mocha": "^5.2.0", "rollup": "^0.66.6", "rollup-plugin-babel": "^4.0.3", "rollup-plugin-commonjs": "^9.2.0", "rollup-plugin-node-resolve": "^3.4.0", "rollup-plugin-replace": "^2.1.0", "rollup-plugin-size-snapshot": "^0.7.0", "rollup-plugin-uglify": "^6.0.0", "webpack": "^3.12.0"}, "browserify": {"transform": ["loose-envify"]}, "keywords": ["history", "location"]}