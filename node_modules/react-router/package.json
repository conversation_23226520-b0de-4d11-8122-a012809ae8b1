{"name": "react-router", "version": "5.3.4", "description": "Declarative routing for React", "homepage": "https://reactrouter.com/", "repository": {"url": "https://github.com/remix-run/react-router.git", "type": "git", "directory": "packages/react-router"}, "license": "MIT", "author": "Remix Software <<EMAIL>>", "files": ["LICENSE", "README.md", "MemoryRouter.js", "Prompt.js", "Redirect.js", "Route.js", "Router.js", "StaticRouter.js", "Switch.js", "cjs", "es", "esm", "index.js", "generatePath.js", "matchPath.js", "modules/*.js", "modules/utils/*.js", "withRouter.js", "warnAboutDeprecatedCJSRequire.js", "umd"], "main": "index.js", "module": "esm/react-router.js", "sideEffects": false, "scripts": {"build": "rollup -c", "lint": "eslint modules"}, "peerDependencies": {"react": ">=15"}, "dependencies": {"@babel/runtime": "^7.12.13", "history": "^4.9.0", "hoist-non-react-statics": "^3.1.0", "loose-envify": "^1.3.1", "path-to-regexp": "^1.7.0", "prop-types": "^15.6.2", "react-is": "^16.6.0", "tiny-invariant": "^1.0.2", "tiny-warning": "^1.0.0"}, "browserify": {"transform": ["loose-envify"]}, "keywords": ["react", "router", "route", "routing", "history", "link"]}