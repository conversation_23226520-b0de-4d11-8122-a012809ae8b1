{"version": 3, "file": "react-router.js", "sources": ["../modules/miniCreateReactContext.js", "../modules/createContext.js", "../modules/createNamedContext.js", "../modules/HistoryContext.js", "../modules/RouterContext.js", "../modules/Router.js", "../modules/MemoryRouter.js", "../modules/Lifecycle.js", "../modules/Prompt.js", "../modules/generatePath.js", "../modules/Redirect.js", "../modules/matchPath.js", "../modules/Route.js", "../modules/StaticRouter.js", "../modules/Switch.js", "../modules/withRouter.js", "../modules/hooks.js", "../modules/index.js"], "sourcesContent": ["// MIT License\n// Copyright (c) 2019-present StringEpsilon <<EMAIL>>\n// Copyright (c) 2017-2019 <PERSON> <<EMAIL>>\n// https://github.com/StringEpsilon/mini-create-react-context\nimport React from \"react\";\nimport PropTypes from \"prop-types\";\nimport warning from \"tiny-warning\";\n\nconst MAX_SIGNED_31_BIT_INT = **********;\n\nconst commonjsGlobal =\n  typeof globalThis !== \"undefined\" // 'global proper'\n    ? // eslint-disable-next-line no-undef\n      globalThis\n    : typeof window !== \"undefined\"\n    ? window // Browser\n    : typeof global !== \"undefined\"\n    ? global // node.js\n    : {};\n\nfunction getUniqueId() {\n  let key = \"__global_unique_id__\";\n  return (commonjsGlobal[key] = (commonjsGlobal[key] || 0) + 1);\n}\n\n// Inlined Object.is polyfill.\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\nfunction objectIs(x, y) {\n  if (x === y) {\n    return x !== 0 || 1 / x === 1 / y;\n  } else {\n    // eslint-disable-next-line no-self-compare\n    return x !== x && y !== y;\n  }\n}\n\nfunction createEventEmitter(value) {\n  let handlers = [];\n  return {\n    on(handler) {\n      handlers.push(handler);\n    },\n\n    off(handler) {\n      handlers = handlers.filter(h => h !== handler);\n    },\n\n    get() {\n      return value;\n    },\n\n    set(newValue, changedBits) {\n      value = newValue;\n      handlers.forEach(handler => handler(value, changedBits));\n    }\n  };\n}\n\nfunction onlyChild(children) {\n  return Array.isArray(children) ? children[0] : children;\n}\n\nexport default function createReactContext(defaultValue, calculateChangedBits) {\n  const contextProp = \"__create-react-context-\" + getUniqueId() + \"__\";\n\n  class Provider extends React.Component {\n    emitter = createEventEmitter(this.props.value);\n\n    static childContextTypes = {\n      [contextProp]: PropTypes.object.isRequired\n    };\n\n    getChildContext() {\n      return {\n        [contextProp]: this.emitter\n      };\n    }\n\n    componentWillReceiveProps(nextProps) {\n      if (this.props.value !== nextProps.value) {\n        let oldValue = this.props.value;\n        let newValue = nextProps.value;\n        let changedBits;\n\n        if (objectIs(oldValue, newValue)) {\n          changedBits = 0; // No change\n        } else {\n          changedBits =\n            typeof calculateChangedBits === \"function\"\n              ? calculateChangedBits(oldValue, newValue)\n              : MAX_SIGNED_31_BIT_INT;\n          if (process.env.NODE_ENV !== \"production\") {\n            warning(\n              (changedBits & MAX_SIGNED_31_BIT_INT) === changedBits,\n              \"calculateChangedBits: Expected the return value to be a \" +\n                \"31-bit integer. Instead received: \" +\n                changedBits\n            );\n          }\n\n          changedBits |= 0;\n\n          if (changedBits !== 0) {\n            this.emitter.set(nextProps.value, changedBits);\n          }\n        }\n      }\n    }\n\n    render() {\n      return this.props.children;\n    }\n  }\n\n  class Consumer extends React.Component {\n    static contextTypes = {\n      [contextProp]: PropTypes.object\n    };\n\n    observedBits;\n\n    state = {\n      value: this.getValue()\n    };\n\n    componentWillReceiveProps(nextProps) {\n      let { observedBits } = nextProps;\n      this.observedBits =\n        observedBits === undefined || observedBits === null\n          ? MAX_SIGNED_31_BIT_INT // Subscribe to all changes by default\n          : observedBits;\n    }\n\n    componentDidMount() {\n      if (this.context[contextProp]) {\n        this.context[contextProp].on(this.onUpdate);\n      }\n      let { observedBits } = this.props;\n      this.observedBits =\n        observedBits === undefined || observedBits === null\n          ? MAX_SIGNED_31_BIT_INT // Subscribe to all changes by default\n          : observedBits;\n    }\n\n    componentWillUnmount() {\n      if (this.context[contextProp]) {\n        this.context[contextProp].off(this.onUpdate);\n      }\n    }\n\n    getValue() {\n      if (this.context[contextProp]) {\n        return this.context[contextProp].get();\n      } else {\n        return defaultValue;\n      }\n    }\n\n    onUpdate = (newValue, changedBits) => {\n      const observedBits = this.observedBits | 0;\n      if ((observedBits & changedBits) !== 0) {\n        this.setState({ value: this.getValue() });\n      }\n    };\n\n    render() {\n      return onlyChild(this.props.children)(this.state.value);\n    }\n  }\n\n  return {\n    Provider,\n    Consumer\n  };\n}\n", "// MIT License\n// Copyright (c) 2019-present StringEpsilon <<EMAIL>>\n// Copyright (c) 2017-2019 <PERSON> <<EMAIL>>\n// https://github.com/StringEpsilon/mini-create-react-context\nimport React from \"react\";\nimport createReactContext from \"./miniCreateReactContext\";\n\nexport default React.createContext || createReactContext;\n", "// TODO: Replace with React.createContext once we can assume React 16+\nimport createContext from \"./createContext\";\n\nconst createNamedContext = name => {\n  const context = createContext();\n  context.displayName = name;\n\n  return context;\n};\n\nexport default createNamedContext;\n", "import createNamedContext from \"./createNamedContext\";\n\nconst historyContext = /*#__PURE__*/ createNamedContext(\"Router-History\");\nexport default historyContext;\n", "import createNamedContext from \"./createNamedContext\";\n\nconst context = /*#__PURE__*/ createNamedContext(\"Router\");\nexport default context;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport warning from \"tiny-warning\";\n\nimport HistoryContext from \"./HistoryContext.js\";\nimport RouterContext from \"./RouterContext.js\";\n\n/**\n * The public API for putting history on context.\n */\nclass Router extends React.Component {\n  static computeRootMatch(pathname) {\n    return { path: \"/\", url: \"/\", params: {}, isExact: pathname === \"/\" };\n  }\n\n  constructor(props) {\n    super(props);\n\n    this.state = {\n      location: props.history.location\n    };\n\n    // This is a bit of a hack. We have to start listening for location\n    // changes here in the constructor in case there are any <Redirect>s\n    // on the initial render. If there are, they will replace/push when\n    // they mount and since cDM fires in children before parents, we may\n    // get a new location before the <Router> is mounted.\n    this._isMounted = false;\n    this._pendingLocation = null;\n\n    if (!props.staticContext) {\n      this.unlisten = props.history.listen(location => {\n        this._pendingLocation = location;\n      });\n    }\n  }\n\n  componentDidMount() {\n    this._isMounted = true;\n\n    if (this.unlisten) {\n      // Any pre-mount location changes have been captured at\n      // this point, so unregister the listener.\n      this.unlisten();\n    }\n    if (!this.props.staticContext) {\n      this.unlisten = this.props.history.listen(location => {\n        if (this._isMounted) {\n          this.setState({ location });\n        }\n      });\n    }\n    if (this._pendingLocation) {\n      this.setState({ location: this._pendingLocation });\n    }\n  }\n\n  componentWillUnmount() {\n    if (this.unlisten) {\n      this.unlisten();\n      this._isMounted = false;\n      this._pendingLocation = null;\n    }\n  }\n\n  render() {\n    return (\n      <RouterContext.Provider\n        value={{\n          history: this.props.history,\n          location: this.state.location,\n          match: Router.computeRootMatch(this.state.location.pathname),\n          staticContext: this.props.staticContext\n        }}\n      >\n        <HistoryContext.Provider\n          children={this.props.children || null}\n          value={this.props.history}\n        />\n      </RouterContext.Provider>\n    );\n  }\n}\n\nif (__DEV__) {\n  Router.propTypes = {\n    children: PropTypes.node,\n    history: PropTypes.object.isRequired,\n    staticContext: PropTypes.object\n  };\n\n  Router.prototype.componentDidUpdate = function(prevProps) {\n    warning(\n      prevProps.history === this.props.history,\n      \"You cannot change <Router history>\"\n    );\n  };\n}\n\nexport default Router;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport { createMemoryHistory as createHistory } from \"history\";\nimport warning from \"tiny-warning\";\n\nimport Router from \"./Router.js\";\n\n/**\n * The public API for a <Router> that stores location in memory.\n */\nclass MemoryRouter extends React.Component {\n  history = createHistory(this.props);\n\n  render() {\n    return <Router history={this.history} children={this.props.children} />;\n  }\n}\n\nif (__DEV__) {\n  MemoryRouter.propTypes = {\n    initialEntries: PropTypes.array,\n    initialIndex: PropTypes.number,\n    getUserConfirmation: PropTypes.func,\n    keyLength: PropTypes.number,\n    children: PropTypes.node\n  };\n\n  MemoryRouter.prototype.componentDidMount = function() {\n    warning(\n      !this.props.history,\n      \"<MemoryRouter> ignores the history prop. To use a custom history, \" +\n        \"use `import { Router }` instead of `import { MemoryRouter as Router }`.\"\n    );\n  };\n}\n\nexport default MemoryRouter;\n", "import React from \"react\";\n\nclass Lifecycle extends React.Component {\n  componentDidMount() {\n    if (this.props.onMount) this.props.onMount.call(this, this);\n  }\n\n  componentDidUpdate(prevProps) {\n    if (this.props.onUpdate) this.props.onUpdate.call(this, this, prevProps);\n  }\n\n  componentWillUnmount() {\n    if (this.props.onUnmount) this.props.onUnmount.call(this, this);\n  }\n\n  render() {\n    return null;\n  }\n}\n\nexport default Lifecycle;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport invariant from \"tiny-invariant\";\n\nimport Lifecycle from \"./Lifecycle.js\";\nimport RouterContext from \"./RouterContext.js\";\n\n/**\n * The public API for prompting the user before navigating away from a screen.\n */\nfunction Prompt({ message, when = true }) {\n  return (\n    <RouterContext.Consumer>\n      {context => {\n        invariant(context, \"You should not use <Prompt> outside a <Router>\");\n\n        if (!when || context.staticContext) return null;\n\n        const method = context.history.block;\n\n        return (\n          <Lifecycle\n            onMount={self => {\n              self.release = method(message);\n            }}\n            onUpdate={(self, prevProps) => {\n              if (prevProps.message !== message) {\n                self.release();\n                self.release = method(message);\n              }\n            }}\n            onUnmount={self => {\n              self.release();\n            }}\n            message={message}\n          />\n        );\n      }}\n    </RouterContext.Consumer>\n  );\n}\n\nif (__DEV__) {\n  const messageType = PropTypes.oneOfType([PropTypes.func, PropTypes.string]);\n\n  Prompt.propTypes = {\n    when: PropTypes.bool,\n    message: messageType.isRequired\n  };\n}\n\nexport default Prompt;\n", "import pathToRegexp from \"path-to-regexp\";\n\nconst cache = {};\nconst cacheLimit = 10000;\nlet cacheCount = 0;\n\nfunction compilePath(path) {\n  if (cache[path]) return cache[path];\n\n  const generator = pathToRegexp.compile(path);\n\n  if (cacheCount < cacheLimit) {\n    cache[path] = generator;\n    cacheCount++;\n  }\n\n  return generator;\n}\n\n/**\n * Public API for generating a URL pathname from a path and parameters.\n */\nfunction generatePath(path = \"/\", params = {}) {\n  return path === \"/\" ? path : compilePath(path)(params, { pretty: true });\n}\n\nexport default generatePath;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport { createLocation, locationsAreEqual } from \"history\";\nimport invariant from \"tiny-invariant\";\n\nimport Lifecycle from \"./Lifecycle.js\";\nimport RouterContext from \"./RouterContext.js\";\nimport generatePath from \"./generatePath.js\";\n\n/**\n * The public API for navigating programmatically with a component.\n */\nfunction Redirect({ computedMatch, to, push = false }) {\n  return (\n    <RouterContext.Consumer>\n      {context => {\n        invariant(context, \"You should not use <Redirect> outside a <Router>\");\n\n        const { history, staticContext } = context;\n\n        const method = push ? history.push : history.replace;\n        const location = createLocation(\n          computedMatch\n            ? typeof to === \"string\"\n              ? generatePath(to, computedMatch.params)\n              : {\n                  ...to,\n                  pathname: generatePath(to.pathname, computedMatch.params)\n                }\n            : to\n        );\n\n        // When rendering in a static context,\n        // set the new location immediately.\n        if (staticContext) {\n          method(location);\n          return null;\n        }\n\n        return (\n          <Lifecycle\n            onMount={() => {\n              method(location);\n            }}\n            onUpdate={(self, prevProps) => {\n              const prevLocation = createLocation(prevProps.to);\n              if (\n                !locationsAreEqual(prevLocation, {\n                  ...location,\n                  key: prevLocation.key\n                })\n              ) {\n                method(location);\n              }\n            }}\n            to={to}\n          />\n        );\n      }}\n    </RouterContext.Consumer>\n  );\n}\n\nif (__DEV__) {\n  Redirect.propTypes = {\n    push: PropTypes.bool,\n    from: PropTypes.string,\n    to: PropTypes.oneOfType([PropTypes.string, PropTypes.object]).isRequired\n  };\n}\n\nexport default Redirect;\n", "import pathToRegexp from \"path-to-regexp\";\n\nconst cache = {};\nconst cacheLimit = 10000;\nlet cacheCount = 0;\n\nfunction compilePath(path, options) {\n  const cacheKey = `${options.end}${options.strict}${options.sensitive}`;\n  const pathCache = cache[cacheKey] || (cache[cacheKey] = {});\n\n  if (pathCache[path]) return pathCache[path];\n\n  const keys = [];\n  const regexp = pathToRegexp(path, keys, options);\n  const result = { regexp, keys };\n\n  if (cacheCount < cacheLimit) {\n    pathCache[path] = result;\n    cacheCount++;\n  }\n\n  return result;\n}\n\n/**\n * Public API for matching a URL pathname to a path.\n */\nfunction matchPath(pathname, options = {}) {\n  if (typeof options === \"string\" || Array.isArray(options)) {\n    options = { path: options };\n  }\n\n  const { path, exact = false, strict = false, sensitive = false } = options;\n\n  const paths = [].concat(path);\n\n  return paths.reduce((matched, path) => {\n    if (!path && path !== \"\") return null;\n    if (matched) return matched;\n\n    const { regexp, keys } = compilePath(path, {\n      end: exact,\n      strict,\n      sensitive\n    });\n    const match = regexp.exec(pathname);\n\n    if (!match) return null;\n\n    const [url, ...values] = match;\n    const isExact = pathname === url;\n\n    if (exact && !isExact) return null;\n\n    return {\n      path, // the path used to match\n      url: path === \"/\" && url === \"\" ? \"/\" : url, // the matched portion of the URL\n      isExact, // whether or not we matched exactly\n      params: keys.reduce((memo, key, index) => {\n        memo[key.name] = values[index];\n        return memo;\n      }, {})\n    };\n  }, null);\n}\n\nexport default matchPath;\n", "import React from \"react\";\nimport { isValidElementType } from \"react-is\";\nimport PropTypes from \"prop-types\";\nimport invariant from \"tiny-invariant\";\nimport warning from \"tiny-warning\";\n\nimport RouterContext from \"./RouterContext.js\";\nimport matchPath from \"./matchPath.js\";\n\nfunction isEmptyChildren(children) {\n  return React.Children.count(children) === 0;\n}\n\nfunction evalChildrenDev(children, props, path) {\n  const value = children(props);\n\n  warning(\n    value !== undefined,\n    \"You returned `undefined` from the `children` function of \" +\n      `<Route${path ? ` path=\"${path}\"` : \"\"}>, but you ` +\n      \"should have returned a React element or `null`\"\n  );\n\n  return value || null;\n}\n\n/**\n * The public API for matching a single path and rendering.\n */\nclass Route extends React.Component {\n  render() {\n    return (\n      <RouterContext.Consumer>\n        {context => {\n          invariant(context, \"You should not use <Route> outside a <Router>\");\n\n          const location = this.props.location || context.location;\n          const match = this.props.computedMatch\n            ? this.props.computedMatch // <Switch> already computed the match for us\n            : this.props.path\n            ? matchPath(location.pathname, this.props)\n            : context.match;\n\n          const props = { ...context, location, match };\n\n          let { children, component, render } = this.props;\n\n          // Preact uses an empty array as children by\n          // default, so use null if that's the case.\n          if (Array.isArray(children) && isEmptyChildren(children)) {\n            children = null;\n          }\n\n          return (\n            <RouterContext.Provider value={props}>\n              {props.match\n                ? children\n                  ? typeof children === \"function\"\n                    ? __DEV__\n                      ? evalChildrenDev(children, props, this.props.path)\n                      : children(props)\n                    : children\n                  : component\n                  ? React.createElement(component, props)\n                  : render\n                  ? render(props)\n                  : null\n                : typeof children === \"function\"\n                ? __DEV__\n                  ? evalChildrenDev(children, props, this.props.path)\n                  : children(props)\n                : null}\n            </RouterContext.Provider>\n          );\n        }}\n      </RouterContext.Consumer>\n    );\n  }\n}\n\nif (__DEV__) {\n  Route.propTypes = {\n    children: PropTypes.oneOfType([PropTypes.func, PropTypes.node]),\n    component: (props, propName) => {\n      if (props[propName] && !isValidElementType(props[propName])) {\n        return new Error(\n          `Invalid prop 'component' supplied to 'Route': the prop is not a valid React component`\n        );\n      }\n    },\n    exact: PropTypes.bool,\n    location: PropTypes.object,\n    path: PropTypes.oneOfType([\n      PropTypes.string,\n      PropTypes.arrayOf(PropTypes.string)\n    ]),\n    render: PropTypes.func,\n    sensitive: PropTypes.bool,\n    strict: PropTypes.bool\n  };\n\n  Route.prototype.componentDidMount = function() {\n    warning(\n      !(\n        this.props.children &&\n        !isEmptyChildren(this.props.children) &&\n        this.props.component\n      ),\n      \"You should not use <Route component> and <Route children> in the same route; <Route component> will be ignored\"\n    );\n\n    warning(\n      !(\n        this.props.children &&\n        !isEmptyChildren(this.props.children) &&\n        this.props.render\n      ),\n      \"You should not use <Route render> and <Route children> in the same route; <Route render> will be ignored\"\n    );\n\n    warning(\n      !(this.props.component && this.props.render),\n      \"You should not use <Route component> and <Route render> in the same route; <Route render> will be ignored\"\n    );\n  };\n\n  Route.prototype.componentDidUpdate = function(prevProps) {\n    warning(\n      !(this.props.location && !prevProps.location),\n      '<Route> elements should not change from uncontrolled to controlled (or vice versa). You initially used no \"location\" prop and then provided one on a subsequent render.'\n    );\n\n    warning(\n      !(!this.props.location && prevProps.location),\n      '<Route> elements should not change from controlled to uncontrolled (or vice versa). You provided a \"location\" prop initially but omitted it on a subsequent render.'\n    );\n  };\n}\n\nexport default Route;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport { createLocation, createPath } from \"history\";\nimport invariant from \"tiny-invariant\";\nimport warning from \"tiny-warning\";\n\nimport Router from \"./Router.js\";\n\nfunction addLeadingSlash(path) {\n  return path.charAt(0) === \"/\" ? path : \"/\" + path;\n}\n\nfunction addBasename(basename, location) {\n  if (!basename) return location;\n\n  return {\n    ...location,\n    pathname: addLeadingSlash(basename) + location.pathname\n  };\n}\n\nfunction stripBasename(basename, location) {\n  if (!basename) return location;\n\n  const base = addLeadingSlash(basename);\n\n  if (location.pathname.indexOf(base) !== 0) return location;\n\n  return {\n    ...location,\n    pathname: location.pathname.substr(base.length)\n  };\n}\n\nfunction createURL(location) {\n  return typeof location === \"string\" ? location : createPath(location);\n}\n\nfunction staticHandler(methodName) {\n  return () => {\n    invariant(false, \"You cannot %s with <StaticRouter>\", methodName);\n  };\n}\n\nfunction noop() {}\n\n/**\n * The public top-level API for a \"static\" <Router>, so-called because it\n * can't actually change the current location. Instead, it just records\n * location changes in a context object. Useful mainly in testing and\n * server-rendering scenarios.\n */\nclass StaticRouter extends React.Component {\n  navigateTo(location, action) {\n    const { basename = \"\", context = {} } = this.props;\n    context.action = action;\n    context.location = addBasename(basename, createLocation(location));\n    context.url = createURL(context.location);\n  }\n\n  handlePush = location => this.navigateTo(location, \"PUSH\");\n  handleReplace = location => this.navigateTo(location, \"REPLACE\");\n  handleListen = () => noop;\n  handleBlock = () => noop;\n\n  render() {\n    const { basename = \"\", context = {}, location = \"/\", ...rest } = this.props;\n\n    const history = {\n      createHref: path => addLeadingSlash(basename + createURL(path)),\n      action: \"POP\",\n      location: stripBasename(basename, createLocation(location)),\n      push: this.handlePush,\n      replace: this.handleReplace,\n      go: staticHandler(\"go\"),\n      goBack: staticHandler(\"goBack\"),\n      goForward: staticHandler(\"goForward\"),\n      listen: this.handleListen,\n      block: this.handleBlock\n    };\n\n    return <Router {...rest} history={history} staticContext={context} />;\n  }\n}\n\nif (__DEV__) {\n  StaticRouter.propTypes = {\n    basename: PropTypes.string,\n    context: PropTypes.object,\n    location: PropTypes.oneOfType([PropTypes.string, PropTypes.object])\n  };\n\n  StaticRouter.prototype.componentDidMount = function() {\n    warning(\n      !this.props.history,\n      \"<StaticRouter> ignores the history prop. To use a custom history, \" +\n        \"use `import { Router }` instead of `import { StaticRouter as Router }`.\"\n    );\n  };\n}\n\nexport default StaticRouter;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport invariant from \"tiny-invariant\";\nimport warning from \"tiny-warning\";\n\nimport RouterContext from \"./RouterContext.js\";\nimport matchPath from \"./matchPath.js\";\n\n/**\n * The public API for rendering the first <Route> that matches.\n */\nclass Switch extends React.Component {\n  render() {\n    return (\n      <RouterContext.Consumer>\n        {context => {\n          invariant(context, \"You should not use <Switch> outside a <Router>\");\n\n          const location = this.props.location || context.location;\n\n          let element, match;\n\n          // We use React.Children.forEach instead of React.Children.toArray().find()\n          // here because toArray adds keys to all child elements and we do not want\n          // to trigger an unmount/remount for two <Route>s that render the same\n          // component at different URLs.\n          React.Children.forEach(this.props.children, child => {\n            if (match == null && React.isValidElement(child)) {\n              element = child;\n\n              const path = child.props.path || child.props.from;\n\n              match = path\n                ? matchPath(location.pathname, { ...child.props, path })\n                : context.match;\n            }\n          });\n\n          return match\n            ? React.cloneElement(element, { location, computedMatch: match })\n            : null;\n        }}\n      </RouterContext.Consumer>\n    );\n  }\n}\n\nif (__DEV__) {\n  Switch.propTypes = {\n    children: PropTypes.node,\n    location: PropTypes.object\n  };\n\n  Switch.prototype.componentDidUpdate = function(prevProps) {\n    warning(\n      !(this.props.location && !prevProps.location),\n      '<Switch> elements should not change from uncontrolled to controlled (or vice versa). You initially used no \"location\" prop and then provided one on a subsequent render.'\n    );\n\n    warning(\n      !(!this.props.location && prevProps.location),\n      '<Switch> elements should not change from controlled to uncontrolled (or vice versa). You provided a \"location\" prop initially but omitted it on a subsequent render.'\n    );\n  };\n}\n\nexport default Switch;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport hoistStatics from \"hoist-non-react-statics\";\nimport invariant from \"tiny-invariant\";\n\nimport RouterContext from \"./RouterContext.js\";\n\n/**\n * A public higher-order component to access the imperative API\n */\nfunction withRouter(Component) {\n  const displayName = `withRouter(${Component.displayName || Component.name})`;\n  const C = props => {\n    const { wrappedComponentRef, ...remainingProps } = props;\n\n    return (\n      <RouterContext.Consumer>\n        {context => {\n          invariant(\n            context,\n            `You should not use <${displayName} /> outside a <Router>`\n          );\n          return (\n            <Component\n              {...remainingProps}\n              {...context}\n              ref={wrappedComponentRef}\n            />\n          );\n        }}\n      </RouterContext.Consumer>\n    );\n  };\n\n  C.displayName = displayName;\n  C.WrappedComponent = Component;\n\n  if (__DEV__) {\n    C.propTypes = {\n      wrappedComponentRef: PropTypes.oneOfType([\n        PropTypes.string,\n        PropTypes.func,\n        PropTypes.object\n      ])\n    };\n  }\n\n  return hoistStatics(C, Component);\n}\n\nexport default withRouter;\n", "import React from \"react\";\nimport invariant from \"tiny-invariant\";\n\nimport RouterContext from \"./RouterContext.js\";\nimport HistoryContext from \"./HistoryContext.js\";\nimport matchPath from \"./matchPath.js\";\n\nconst useContext = React.useContext;\n\nexport function useHistory() {\n  if (__DEV__) {\n    invariant(\n      typeof useContext === \"function\",\n      \"You must use React >= 16.8 in order to use useHistory()\"\n    );\n  }\n\n  return useContext(HistoryContext);\n}\n\nexport function useLocation() {\n  if (__DEV__) {\n    invariant(\n      typeof useContext === \"function\",\n      \"You must use React >= 16.8 in order to use useLocation()\"\n    );\n  }\n\n  return useContext(RouterContext).location;\n}\n\nexport function useParams() {\n  if (__DEV__) {\n    invariant(\n      typeof useContext === \"function\",\n      \"You must use React >= 16.8 in order to use useParams()\"\n    );\n  }\n\n  const match = useContext(RouterContext).match;\n  return match ? match.params : {};\n}\n\nexport function useRouteMatch(path) {\n  if (__DEV__) {\n    invariant(\n      typeof useContext === \"function\",\n      \"You must use React >= 16.8 in order to use useRouteMatch()\"\n    );\n  }\n\n  const location = useLocation();\n  const match = useContext(RouterContext).match;\n  return path ? matchPath(location.pathname, path) : match;\n}\n", "if (__DEV__) {\n  if (typeof window !== \"undefined\") {\n    const global = window;\n    const key = \"__react_router_build__\";\n    const buildNames = { cjs: \"CommonJS\", esm: \"ES modules\", umd: \"UMD\" };\n\n    if (global[key] && global[key] !== process.env.BUILD_FORMAT) {\n      const initialBuildName = buildNames[global[key]];\n      const secondaryBuildName = buildNames[process.env.BUILD_FORMAT];\n\n      // TODO: Add link to article that explains in detail how to avoid\n      // loading 2 different builds.\n      throw new Error(\n        `You are loading the ${secondaryBuildName} build of React Router ` +\n          `on a page that is already running the ${initialBuildName} ` +\n          `build, so things won't work right.`\n      );\n    }\n\n    global[key] = process.env.BUILD_FORMAT;\n  }\n}\n\nexport { default as MemoryRouter } from \"./MemoryRouter.js\";\nexport { default as Prompt } from \"./Prompt.js\";\nexport { default as Redirect } from \"./Redirect.js\";\nexport { default as Route } from \"./Route.js\";\nexport { default as Router } from \"./Router.js\";\nexport { default as StaticRouter } from \"./StaticRouter.js\";\nexport { default as Switch } from \"./Switch.js\";\nexport { default as generatePath } from \"./generatePath.js\";\nexport { default as matchPath } from \"./matchPath.js\";\nexport { default as withRouter } from \"./withRouter.js\";\n\nexport { default as __HistoryContext } from \"./HistoryContext.js\";\nexport { default as __RouterContext } from \"./RouterContext.js\";\n\nexport { useHistory, useLocation, useParams, useRouteMatch } from \"./hooks.js\";\n"], "names": ["MAX_SIGNED_31_BIT_INT", "commonjsGlobal", "globalThis", "window", "global", "getUniqueId", "key", "objectIs", "x", "y", "createEventEmitter", "value", "handlers", "on", "handler", "push", "off", "filter", "h", "get", "set", "newValue", "changedBits", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>", "children", "Array", "isArray", "createReactContext", "defaultValue", "calculateChangedBits", "contextProp", "Provider", "emitter", "props", "getChildContext", "componentWillReceiveProps", "nextProps", "oldValue", "process", "env", "NODE_ENV", "warning", "render", "React", "Component", "childContextTypes", "PropTypes", "object", "isRequired", "Consumer", "observedBits", "state", "getValue", "onUpdate", "setState", "undefined", "componentDidMount", "context", "componentWillUnmount", "contextTypes", "createContext", "createNamedContext", "name", "displayName", "historyContext", "Router", "computeRootMatch", "pathname", "path", "url", "params", "isExact", "location", "history", "_isMounted", "_pendingLocation", "staticContext", "unlisten", "listen", "RouterContext", "match", "HistoryContext", "propTypes", "node", "prototype", "componentDidUpdate", "prevProps", "MemoryRouter", "createHistory", "initialEntries", "array", "initialIndex", "number", "getUserConfirmation", "func", "<PERSON><PERSON><PERSON><PERSON>", "Lifecycle", "onMount", "call", "onUnmount", "Prompt", "message", "when", "invariant", "method", "block", "self", "release", "messageType", "oneOfType", "string", "bool", "cache", "cacheLimit", "cacheCount", "compilePath", "generator", "pathToRegexp", "compile", "generatePath", "pretty", "Redirect", "computedMatch", "to", "replace", "createLocation", "prevLocation", "locationsAreEqual", "from", "options", "cache<PERSON>ey", "end", "strict", "sensitive", "pathCache", "keys", "regexp", "result", "matchPath", "exact", "paths", "concat", "reduce", "matched", "exec", "values", "memo", "index", "isEmptyChildren", "Children", "count", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Route", "component", "createElement", "propName", "isValidElementType", "Error", "arrayOf", "addLeadingSlash", "char<PERSON>t", "addBasename", "basename", "stripBasename", "base", "indexOf", "substr", "length", "createURL", "createPath", "static<PERSON><PERSON><PERSON>", "methodName", "noop", "StaticRouter", "handlePush", "navigateTo", "handleReplace", "handleListen", "handleBlock", "action", "rest", "createHref", "go", "goBack", "goForward", "Switch", "element", "child", "isValidElement", "cloneElement", "with<PERSON><PERSON><PERSON>", "C", "wrappedComponentRef", "remainingProps", "WrappedComponent", "hoistStatics", "useContext", "useHistory", "useLocation", "useParams", "useRouteMatch", "buildNames", "cjs", "esm", "umd", "initialBuildName", "secondaryBuildName"], "mappings": ";;;;;;;;;;;;AAQA,IAAMA,qBAAqB,GAAG,UAA9B;AAEA,IAAMC,cAAc,GAClB,OAAOC,UAAP,KAAsB,WAAtB;;AAEIA,UAFJ,GAGI,OAAOC,MAAP,KAAkB,WAAlB,GACAA,MADA;EAEA,OAAOC,MAAP,KAAkB,WAAlB,GACAA,MADA;EAEA,EARN;;AAUA,SAASC,WAAT,GAAuB;MACjBC,GAAG,GAAG,sBAAV;SACQL,cAAc,CAACK,GAAD,CAAd,GAAsB,CAACL,cAAc,CAACK,GAAD,CAAd,IAAuB,CAAxB,IAA6B,CAA3D;;;;;AAKF,SAASC,QAAT,CAAkBC,CAAlB,EAAqBC,CAArB,EAAwB;MAClBD,CAAC,KAAKC,CAAV,EAAa;WACJD,CAAC,KAAK,CAAN,IAAW,IAAIA,CAAJ,KAAU,IAAIC,CAAhC;GADF,MAEO;;WAEED,CAAC,KAAKA,CAAN,IAAWC,CAAC,KAAKA,CAAxB;;;;AAIJ,SAASC,kBAAT,CAA4BC,KAA5B,EAAmC;MAC7BC,QAAQ,GAAG,EAAf;SACO;IACLC,EADK,cACFC,OADE,EACO;MACVF,QAAQ,CAACG,IAAT,CAAcD,OAAd;KAFG;IAKLE,GALK,eAKDF,OALC,EAKQ;MACXF,QAAQ,GAAGA,QAAQ,CAACK,MAAT,CAAgB,UAAAC,CAAC;eAAIA,CAAC,KAAKJ,OAAV;OAAjB,CAAX;KANG;IASLK,GATK,iBASC;aACGR,KAAP;KAVG;IAaLS,GAbK,eAaDC,QAbC,EAaSC,WAbT,EAasB;MACzBX,KAAK,GAAGU,QAAR;MACAT,QAAQ,CAACW,OAAT,CAAiB,UAAAT,OAAO;eAAIA,OAAO,CAACH,KAAD,EAAQW,WAAR,CAAX;OAAxB;;GAfJ;;;AAoBF,SAASE,SAAT,CAAmBC,QAAnB,EAA6B;SACpBC,KAAK,CAACC,OAAN,CAAcF,QAAd,IAA0BA,QAAQ,CAAC,CAAD,CAAlC,GAAwCA,QAA/C;;;AAGF,AAAe,SAASG,kBAAT,CAA4BC,YAA5B,EAA0CC,oBAA1C,EAAgE;;;MACvEC,WAAW,GAAG,4BAA4B1B,WAAW,EAAvC,GAA4C,IAAhE;;MAEM2B,QAHuE;;;;;;;;;;;YAI3EC,OAJ2E,GAIjEvB,kBAAkB,CAAC,MAAKwB,KAAL,CAAWvB,KAAZ,CAJ+C;;;;;;WAU3EwB,eAV2E,GAU3E,2BAAkB;;;6BAEbJ,WADH,IACiB,KAAKE,OADtB;KAXyE;;WAgB3EG,yBAhB2E,GAgB3E,mCAA0BC,SAA1B,EAAqC;UAC/B,KAAKH,KAAL,CAAWvB,KAAX,KAAqB0B,SAAS,CAAC1B,KAAnC,EAA0C;YACpC2B,QAAQ,GAAG,KAAKJ,KAAL,CAAWvB,KAA1B;YACIU,QAAQ,GAAGgB,SAAS,CAAC1B,KAAzB;YACIW,WAAJ;;YAEIf,QAAQ,CAAC+B,QAAD,EAAWjB,QAAX,CAAZ,EAAkC;UAChCC,WAAW,GAAG,CAAd,CADgC;SAAlC,MAEO;UACLA,WAAW,GACT,OAAOQ,oBAAP,KAAgC,UAAhC,GACIA,oBAAoB,CAACQ,QAAD,EAAWjB,QAAX,CADxB,GAEIrB,qBAHN;;cAIIuC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;oDACzCC,OAAO,CACL,CAACpB,WAAW,GAAGtB,qBAAf,MAA0CsB,WADrC,EAEL,6DACE,oCADF,GAEEA,WAJG,CAAP;;;UAQFA,WAAW,IAAI,CAAf;;cAEIA,WAAW,KAAK,CAApB,EAAuB;iBAChBW,OAAL,CAAab,GAAb,CAAiBiB,SAAS,CAAC1B,KAA3B,EAAkCW,WAAlC;;;;KAzCmE;;WA+C3EqB,MA/C2E,GA+C3E,kBAAS;aACA,KAAKT,KAAL,CAAWT,QAAlB;KAhDyE;;;IAGtDmB,KAAK,CAACC,SAHgD;;EAGvEb,QAHuE,CAMpEc,iBANoE,sDAOxEf,WAPwE,IAO1DgB,SAAS,CAACC,MAAV,CAAiBC,UAPyC;;MAoDvEC,QApDuE;;;;;;;;;;;aAyD3EC,YAzD2E;aA2D3EC,KA3D2E,GA2DnE;QACNzC,KAAK,EAAE,OAAK0C,QAAL;OA5DkE;;aAgG3EC,QAhG2E,GAgGhE,UAACjC,QAAD,EAAWC,WAAX,EAA2B;YAC9B6B,YAAY,GAAG,OAAKA,YAAL,GAAoB,CAAzC;;YACI,CAACA,YAAY,GAAG7B,WAAhB,MAAiC,CAArC,EAAwC;iBACjCiC,QAAL,CAAc;YAAE5C,KAAK,EAAE,OAAK0C,QAAL;WAAvB;;OAnGuE;;;;;;;YA+D3EjB,yBA/D2E,GA+D3E,mCAA0BC,SAA1B,EAAqC;UAC7Bc,YAD6B,GACZd,SADY,CAC7Bc,YAD6B;WAE9BA,YAAL,GACEA,YAAY,KAAKK,SAAjB,IAA8BL,YAAY,KAAK,IAA/C,GACInD,qBADJ;QAEImD,YAHN;KAjEyE;;YAuE3EM,iBAvE2E,GAuE3E,6BAAoB;UACd,KAAKC,OAAL,CAAa3B,WAAb,CAAJ,EAA+B;aACxB2B,OAAL,CAAa3B,WAAb,EAA0BlB,EAA1B,CAA6B,KAAKyC,QAAlC;;;UAEIH,YAJY,GAIK,KAAKjB,KAJV,CAIZiB,YAJY;WAKbA,YAAL,GACEA,YAAY,KAAKK,SAAjB,IAA8BL,YAAY,KAAK,IAA/C,GACInD,qBADJ;QAEImD,YAHN;KA5EyE;;YAkF3EQ,oBAlF2E,GAkF3E,gCAAuB;UACjB,KAAKD,OAAL,CAAa3B,WAAb,CAAJ,EAA+B;aACxB2B,OAAL,CAAa3B,WAAb,EAA0Bf,GAA1B,CAA8B,KAAKsC,QAAnC;;KApFuE;;YAwF3ED,QAxF2E,GAwF3E,oBAAW;UACL,KAAKK,OAAL,CAAa3B,WAAb,CAAJ,EAA+B;eACtB,KAAK2B,OAAL,CAAa3B,WAAb,EAA0BZ,GAA1B,EAAP;OADF,MAEO;eACEU,YAAP;;KA5FuE;;YAuG3Ec,MAvG2E,GAuG3E,kBAAS;aACAnB,SAAS,CAAC,KAAKU,KAAL,CAAWT,QAAZ,CAAT,CAA+B,KAAK2B,KAAL,CAAWzC,KAA1C,CAAP;KAxGyE;;;IAoDtDiC,KAAK,CAACC,SApDgD;;EAoDvEK,QApDuE,CAqDpEU,YArDoE,sDAsDxE7B,WAtDwE,IAsD1DgB,SAAS,CAACC,MAtDgD;SA4GtE;IACLhB,QAAQ,EAARA,QADK;IAELkB,QAAQ,EAARA;GAFF;;;AC1KF;AACA,AAMA,oBAAeN,KAAK,CAACiB,aAAN,IAAuBjC,kBAAtC;;ACPA;AACA;AAEA,IAAMkC,kBAAkB,GAAG,SAArBA,kBAAqB,CAAAC,IAAI,EAAI;MAC3BL,OAAO,GAAGG,aAAa,EAA7B;EACAH,OAAO,CAACM,WAAR,GAAsBD,IAAtB;SAEOL,OAAP;CAJF;;ACDA,IAAMO,cAAc,gBAAiBH,kBAAkB,CAAC,gBAAD,CAAvD;;ACAA,IAAMJ,OAAO,gBAAiBI,kBAAkB,CAAC,QAAD,CAAhD;;ACKA;;;;IAGMI;;;SACGC,mBAAP,0BAAwBC,QAAxB,EAAkC;WACzB;MAAEC,IAAI,EAAE,GAAR;MAAaC,GAAG,EAAE,GAAlB;MAAuBC,MAAM,EAAE,EAA/B;MAAmCC,OAAO,EAAEJ,QAAQ,KAAK;KAAhE;;;kBAGUlC,KAAZ,EAAmB;;;wCACXA,KAAN;UAEKkB,KAAL,GAAa;MACXqB,QAAQ,EAAEvC,KAAK,CAACwC,OAAN,CAAcD;KAD1B,CAHiB;;;;;;UAYZE,UAAL,GAAkB,KAAlB;UACKC,gBAAL,GAAwB,IAAxB;;QAEI,CAAC1C,KAAK,CAAC2C,aAAX,EAA0B;YACnBC,QAAL,GAAgB5C,KAAK,CAACwC,OAAN,CAAcK,MAAd,CAAqB,UAAAN,QAAQ,EAAI;cAC1CG,gBAAL,GAAwBH,QAAxB;OADc,CAAhB;;;;;;;;SAMJhB,oBAAA,6BAAoB;;;SACbkB,UAAL,GAAkB,IAAlB;;QAEI,KAAKG,QAAT,EAAmB;;;WAGZA,QAAL;;;QAEE,CAAC,KAAK5C,KAAL,CAAW2C,aAAhB,EAA+B;WACxBC,QAAL,GAAgB,KAAK5C,KAAL,CAAWwC,OAAX,CAAmBK,MAAnB,CAA0B,UAAAN,QAAQ,EAAI;YAChD,MAAI,CAACE,UAAT,EAAqB;UACnB,MAAI,CAACpB,QAAL,CAAc;YAAEkB,QAAQ,EAARA;WAAhB;;OAFY,CAAhB;;;QAME,KAAKG,gBAAT,EAA2B;WACpBrB,QAAL,CAAc;QAAEkB,QAAQ,EAAE,KAAKG;OAA/B;;;;SAIJjB,uBAAA,gCAAuB;QACjB,KAAKmB,QAAT,EAAmB;WACZA,QAAL;WACKH,UAAL,GAAkB,KAAlB;WACKC,gBAAL,GAAwB,IAAxB;;;;SAIJjC,SAAA,kBAAS;wBAEL,oBAACqC,OAAD,CAAe,QAAf;MACE,KAAK,EAAE;QACLN,OAAO,EAAE,KAAKxC,KAAL,CAAWwC,OADf;QAELD,QAAQ,EAAE,KAAKrB,KAAL,CAAWqB,QAFhB;QAGLQ,KAAK,EAAEf,MAAM,CAACC,gBAAP,CAAwB,KAAKf,KAAL,CAAWqB,QAAX,CAAoBL,QAA5C,CAHF;QAILS,aAAa,EAAE,KAAK3C,KAAL,CAAW2C;;oBAG5B,oBAACK,cAAD,CAAgB,QAAhB;MACE,QAAQ,EAAE,KAAKhD,KAAL,CAAWT,QAAX,IAAuB,IADnC;MAEE,KAAK,EAAE,KAAKS,KAAL,CAAWwC;MAVtB,CADF;;;;EAxDiB9B,KAAK,CAACC;;AA0E3B,2CAAa;EACXqB,MAAM,CAACiB,SAAP,GAAmB;IACjB1D,QAAQ,EAAEsB,SAAS,CAACqC,IADH;IAEjBV,OAAO,EAAE3B,SAAS,CAACC,MAAV,CAAiBC,UAFT;IAGjB4B,aAAa,EAAE9B,SAAS,CAACC;GAH3B;;EAMAkB,MAAM,CAACmB,SAAP,CAAiBC,kBAAjB,GAAsC,UAASC,SAAT,EAAoB;4CACxD7C,OAAO,CACL6C,SAAS,CAACb,OAAV,KAAsB,KAAKxC,KAAL,CAAWwC,OAD5B,EAEL,oCAFK,CAAP;GADF;;;ACpFF;;;;IAGMc;;;;;;;;;;;UACJd,UAAUe,mBAAa,CAAC,MAAKvD,KAAN;;;;;;SAEvBS,SAAA,kBAAS;wBACA,oBAAC,MAAD;MAAQ,OAAO,EAAE,KAAK+B,OAAtB;MAA+B,QAAQ,EAAE,KAAKxC,KAAL,CAAWT;MAA3D;;;;EAJuBmB,KAAK,CAACC;;AAQjC,2CAAa;EACX2C,YAAY,CAACL,SAAb,GAAyB;IACvBO,cAAc,EAAE3C,SAAS,CAAC4C,KADH;IAEvBC,YAAY,EAAE7C,SAAS,CAAC8C,MAFD;IAGvBC,mBAAmB,EAAE/C,SAAS,CAACgD,IAHR;IAIvBC,SAAS,EAAEjD,SAAS,CAAC8C,MAJE;IAKvBpE,QAAQ,EAAEsB,SAAS,CAACqC;GALtB;;EAQAI,YAAY,CAACH,SAAb,CAAuB5B,iBAAvB,GAA2C,YAAW;4CACpDf,OAAO,CACL,CAAC,KAAKR,KAAL,CAAWwC,OADP,EAEL,uEACE,yEAHG,CAAP;GADF;;;ICzBIuB;;;;;;;;;SACJxC,oBAAA,6BAAoB;QACd,KAAKvB,KAAL,CAAWgE,OAAf,EAAwB,KAAKhE,KAAL,CAAWgE,OAAX,CAAmBC,IAAnB,CAAwB,IAAxB,EAA8B,IAA9B;;;SAG1Bb,qBAAA,4BAAmBC,SAAnB,EAA8B;QACxB,KAAKrD,KAAL,CAAWoB,QAAf,EAAyB,KAAKpB,KAAL,CAAWoB,QAAX,CAAoB6C,IAApB,CAAyB,IAAzB,EAA+B,IAA/B,EAAqCZ,SAArC;;;SAG3B5B,uBAAA,gCAAuB;QACjB,KAAKzB,KAAL,CAAWkE,SAAf,EAA0B,KAAKlE,KAAL,CAAWkE,SAAX,CAAqBD,IAArB,CAA0B,IAA1B,EAAgC,IAAhC;;;SAG5BxD,SAAA,kBAAS;WACA,IAAP;;;;EAdoBC,KAAK,CAACC;;ACK9B;;;;AAGA,SAASwD,MAAT,OAA0C;MAAxBC,OAAwB,QAAxBA,OAAwB;uBAAfC,IAAe;MAAfA,IAAe,0BAAR,IAAQ;sBAEtC,oBAACvB,OAAD,CAAe,QAAf,QACG,UAAAtB,OAAO,EAAI;KACAA,OAAV,2CAAA8C,SAAS,QAAU,gDAAV,CAAT,GAAAA,SAAS,OAAT;QAEI,CAACD,IAAD,IAAS7C,OAAO,CAACmB,aAArB,EAAoC,OAAO,IAAP;QAE9B4B,MAAM,GAAG/C,OAAO,CAACgB,OAAR,CAAgBgC,KAA/B;wBAGE,oBAAC,SAAD;MACE,OAAO,EAAE,iBAAAC,IAAI,EAAI;QACfA,IAAI,CAACC,OAAL,GAAeH,MAAM,CAACH,OAAD,CAArB;OAFJ;MAIE,QAAQ,EAAE,kBAACK,IAAD,EAAOpB,SAAP,EAAqB;YACzBA,SAAS,CAACe,OAAV,KAAsBA,OAA1B,EAAmC;UACjCK,IAAI,CAACC,OAAL;UACAD,IAAI,CAACC,OAAL,GAAeH,MAAM,CAACH,OAAD,CAArB;;OAPN;MAUE,SAAS,EAAE,mBAAAK,IAAI,EAAI;QACjBA,IAAI,CAACC,OAAL;OAXJ;MAaE,OAAO,EAAEN;MAdb;GARJ,CADF;;;AA+BF,2CAAa;MACLO,WAAW,GAAG9D,SAAS,CAAC+D,SAAV,CAAoB,CAAC/D,SAAS,CAACgD,IAAX,EAAiBhD,SAAS,CAACgE,MAA3B,CAApB,CAApB;EAEAV,MAAM,CAAClB,SAAP,GAAmB;IACjBoB,IAAI,EAAExD,SAAS,CAACiE,IADC;IAEjBV,OAAO,EAAEO,WAAW,CAAC5D;GAFvB;;;AC3CF,IAAMgE,KAAK,GAAG,EAAd;AACA,IAAMC,UAAU,GAAG,KAAnB;AACA,IAAIC,UAAU,GAAG,CAAjB;;AAEA,SAASC,WAAT,CAAqB/C,IAArB,EAA2B;MACrB4C,KAAK,CAAC5C,IAAD,CAAT,EAAiB,OAAO4C,KAAK,CAAC5C,IAAD,CAAZ;MAEXgD,SAAS,GAAGC,YAAY,CAACC,OAAb,CAAqBlD,IAArB,CAAlB;;MAEI8C,UAAU,GAAGD,UAAjB,EAA6B;IAC3BD,KAAK,CAAC5C,IAAD,CAAL,GAAcgD,SAAd;IACAF,UAAU;;;SAGLE,SAAP;;;;;;;AAMF,SAASG,YAAT,CAAsBnD,IAAtB,EAAkCE,MAAlC,EAA+C;MAAzBF,IAAyB;IAAzBA,IAAyB,GAAlB,GAAkB;;;MAAbE,MAAa;IAAbA,MAAa,GAAJ,EAAI;;;SACtCF,IAAI,KAAK,GAAT,GAAeA,IAAf,GAAsB+C,WAAW,CAAC/C,IAAD,CAAX,CAAkBE,MAAlB,EAA0B;IAAEkD,MAAM,EAAE;GAApC,CAA7B;;;ACdF;;;;AAGA,SAASC,QAAT,OAAuD;MAAnCC,aAAmC,QAAnCA,aAAmC;MAApBC,EAAoB,QAApBA,EAAoB;uBAAhB7G,IAAgB;MAAhBA,IAAgB,0BAAT,KAAS;sBAEnD,oBAACiE,OAAD,CAAe,QAAf,QACG,UAAAtB,OAAO,EAAI;KACAA,OAAV,2CAAA8C,SAAS,QAAU,kDAAV,CAAT,GAAAA,SAAS,OAAT;QAEQ9B,OAHE,GAGyBhB,OAHzB,CAGFgB,OAHE;QAGOG,aAHP,GAGyBnB,OAHzB,CAGOmB,aAHP;QAKJ4B,MAAM,GAAG1F,IAAI,GAAG2D,OAAO,CAAC3D,IAAX,GAAkB2D,OAAO,CAACmD,OAA7C;QACMpD,QAAQ,GAAGqD,cAAc,CAC7BH,aAAa,GACT,OAAOC,EAAP,KAAc,QAAd,GACEJ,YAAY,CAACI,EAAD,EAAKD,aAAa,CAACpD,MAAnB,CADd,gBAGOqD,EAHP;MAIIxD,QAAQ,EAAEoD,YAAY,CAACI,EAAE,CAACxD,QAAJ,EAAcuD,aAAa,CAACpD,MAA5B;MALjB,GAOTqD,EARyB,CAA/B,CANU;;;QAmBN/C,aAAJ,EAAmB;MACjB4B,MAAM,CAAChC,QAAD,CAAN;aACO,IAAP;;;wBAIA,oBAAC,SAAD;MACE,OAAO,EAAE,mBAAM;QACbgC,MAAM,CAAChC,QAAD,CAAN;OAFJ;MAIE,QAAQ,EAAE,kBAACkC,IAAD,EAAOpB,SAAP,EAAqB;YACvBwC,YAAY,GAAGD,cAAc,CAACvC,SAAS,CAACqC,EAAX,CAAnC;;YAEE,CAACI,iBAAiB,CAACD,YAAD,eACbtD,QADa;UAEhBnE,GAAG,EAAEyH,YAAY,CAACzH;WAHtB,EAKE;UACAmG,MAAM,CAAChC,QAAD,CAAN;;OAZN;MAeE,EAAE,EAAEmD;MAhBR;GAzBJ,CADF;;;AAkDF,2CAAa;EACXF,QAAQ,CAACvC,SAAT,GAAqB;IACnBpE,IAAI,EAAEgC,SAAS,CAACiE,IADG;IAEnBiB,IAAI,EAAElF,SAAS,CAACgE,MAFG;IAGnBa,EAAE,EAAE7E,SAAS,CAAC+D,SAAV,CAAoB,CAAC/D,SAAS,CAACgE,MAAX,EAAmBhE,SAAS,CAACC,MAA7B,CAApB,EAA0DC;GAHhE;;;AC9DF,IAAMgE,OAAK,GAAG,EAAd;AACA,IAAMC,YAAU,GAAG,KAAnB;AACA,IAAIC,YAAU,GAAG,CAAjB;;AAEA,SAASC,aAAT,CAAqB/C,IAArB,EAA2B6D,OAA3B,EAAoC;MAC5BC,QAAQ,QAAMD,OAAO,CAACE,GAAd,GAAoBF,OAAO,CAACG,MAA5B,GAAqCH,OAAO,CAACI,SAA3D;MACMC,SAAS,GAAGtB,OAAK,CAACkB,QAAD,CAAL,KAAoBlB,OAAK,CAACkB,QAAD,CAAL,GAAkB,EAAtC,CAAlB;MAEII,SAAS,CAAClE,IAAD,CAAb,EAAqB,OAAOkE,SAAS,CAAClE,IAAD,CAAhB;MAEfmE,IAAI,GAAG,EAAb;MACMC,MAAM,GAAGnB,YAAY,CAACjD,IAAD,EAAOmE,IAAP,EAAaN,OAAb,CAA3B;MACMQ,MAAM,GAAG;IAAED,MAAM,EAANA,MAAF;IAAUD,IAAI,EAAJA;GAAzB;;MAEIrB,YAAU,GAAGD,YAAjB,EAA6B;IAC3BqB,SAAS,CAAClE,IAAD,CAAT,GAAkBqE,MAAlB;IACAvB,YAAU;;;SAGLuB,MAAP;;;;;;;AAMF,SAASC,SAAT,CAAmBvE,QAAnB,EAA6B8D,OAA7B,EAA2C;MAAdA,OAAc;IAAdA,OAAc,GAAJ,EAAI;;;MACrC,OAAOA,OAAP,KAAmB,QAAnB,IAA+BxG,KAAK,CAACC,OAAN,CAAcuG,OAAd,CAAnC,EAA2D;IACzDA,OAAO,GAAG;MAAE7D,IAAI,EAAE6D;KAAlB;;;iBAGiEA,OAL1B;MAKjC7D,IALiC,YAKjCA,IALiC;gCAK3BuE,KAL2B;MAK3BA,KAL2B,+BAKnB,KALmB;iCAKZP,MALY;MAKZA,MALY,gCAKH,KALG;oCAKIC,SALJ;MAKIA,SALJ,mCAKgB,KALhB;MAOnCO,KAAK,GAAG,GAAGC,MAAH,CAAUzE,IAAV,CAAd;SAEOwE,KAAK,CAACE,MAAN,CAAa,UAACC,OAAD,EAAU3E,IAAV,EAAmB;QACjC,CAACA,IAAD,IAASA,IAAI,KAAK,EAAtB,EAA0B,OAAO,IAAP;QACtB2E,OAAJ,EAAa,OAAOA,OAAP;;uBAEY5B,aAAW,CAAC/C,IAAD,EAAO;MACzC+D,GAAG,EAAEQ,KADoC;MAEzCP,MAAM,EAANA,MAFyC;MAGzCC,SAAS,EAATA;KAHkC,CAJC;QAI7BG,MAJ6B,gBAI7BA,MAJ6B;QAIrBD,IAJqB,gBAIrBA,IAJqB;;QAS/BvD,KAAK,GAAGwD,MAAM,CAACQ,IAAP,CAAY7E,QAAZ,CAAd;QAEI,CAACa,KAAL,EAAY,OAAO,IAAP;QAELX,GAb8B,GAaZW,KAbY;QAatBiE,MAbsB,GAaZjE,KAbY;QAc/BT,OAAO,GAAGJ,QAAQ,KAAKE,GAA7B;QAEIsE,KAAK,IAAI,CAACpE,OAAd,EAAuB,OAAO,IAAP;WAEhB;MACLH,IAAI,EAAJA,IADK;;MAELC,GAAG,EAAED,IAAI,KAAK,GAAT,IAAgBC,GAAG,KAAK,EAAxB,GAA6B,GAA7B,GAAmCA,GAFnC;;MAGLE,OAAO,EAAPA,OAHK;;MAILD,MAAM,EAAEiE,IAAI,CAACO,MAAL,CAAY,UAACI,IAAD,EAAO7I,GAAP,EAAY8I,KAAZ,EAAsB;QACxCD,IAAI,CAAC7I,GAAG,CAACyD,IAAL,CAAJ,GAAiBmF,MAAM,CAACE,KAAD,CAAvB;eACOD,IAAP;OAFM,EAGL,EAHK;KAJV;GAlBK,EA2BJ,IA3BI,CAAP;;;AC3BF,SAASE,eAAT,CAAyB5H,QAAzB,EAAmC;SAC1BmB,KAAK,CAAC0G,QAAN,CAAeC,KAAf,CAAqB9H,QAArB,MAAmC,CAA1C;;;AAGF,SAAS+H,eAAT,CAAyB/H,QAAzB,EAAmCS,KAAnC,EAA0CmC,IAA1C,EAAgD;MACxC1D,KAAK,GAAGc,QAAQ,CAACS,KAAD,CAAtB;0CAEAQ,OAAO,CACL/B,KAAK,KAAK6C,SADL,EAEL,2EACWa,IAAI,gBAAaA,IAAb,UAAuB,EADtC,qBAEE,gDAJG,CAAP;SAOO1D,KAAK,IAAI,IAAhB;;;;;;;IAMI8I;;;;;;;;;SACJ9G,SAAA,kBAAS;;;wBAEL,oBAACqC,OAAD,CAAe,QAAf,QACG,UAAAtB,SAAO,EAAI;OACAA,SAAV,2CAAA8C,SAAS,QAAU,+CAAV,CAAT,GAAAA,SAAS,OAAT;UAEM/B,QAAQ,GAAG,KAAI,CAACvC,KAAL,CAAWuC,QAAX,IAAuBf,SAAO,CAACe,QAAhD;UACMQ,KAAK,GAAG,KAAI,CAAC/C,KAAL,CAAWyF,aAAX,GACV,KAAI,CAACzF,KAAL,CAAWyF,aADD;QAEV,KAAI,CAACzF,KAAL,CAAWmC,IAAX,GACAsE,SAAS,CAAClE,QAAQ,CAACL,QAAV,EAAoB,KAAI,CAAClC,KAAzB,CADT,GAEAwB,SAAO,CAACuB,KAJZ;;UAMM/C,KAAK,gBAAQwB,SAAR;QAAiBe,QAAQ,EAARA,QAAjB;QAA2BQ,KAAK,EAALA;QAAtC;;wBAEsC,KAAI,CAAC/C,KAZjC;UAYJT,QAZI,eAYJA,QAZI;UAYMiI,SAZN,eAYMA,SAZN;UAYiB/G,MAZjB,eAYiBA,MAZjB;;;UAgBNjB,KAAK,CAACC,OAAN,CAAcF,QAAd,KAA2B4H,eAAe,CAAC5H,QAAD,CAA9C,EAA0D;QACxDA,QAAQ,GAAG,IAAX;;;0BAIA,oBAACuD,OAAD,CAAe,QAAf;QAAwB,KAAK,EAAE9C;SAC5BA,KAAK,CAAC+C,KAAN,GACGxD,QAAQ,GACN,OAAOA,QAAP,KAAoB,UAApB,GACE,wCACE+H,eAAe,CAAC/H,QAAD,EAAWS,KAAX,EAAkB,KAAI,CAACA,KAAL,CAAWmC,IAA7B,CADjB,GAEE5C,QAAQ,CAACS,KAAD,CAHZ,GAIET,QALI,GAMNiI,SAAS,gBACT9G,KAAK,CAAC+G,aAAN,CAAoBD,SAApB,EAA+BxH,KAA/B,CADS,GAETS,MAAM,GACNA,MAAM,CAACT,KAAD,CADA,GAEN,IAXL,GAYG,OAAOT,QAAP,KAAoB,UAApB,GACA,wCACE+H,eAAe,CAAC/H,QAAD,EAAWS,KAAX,EAAkB,KAAI,CAACA,KAAL,CAAWmC,IAA7B,CADjB,GAEE5C,QAAQ,CAACS,KAAD,CAHV,GAIA,IAjBN,CADF;KArBJ,CADF;;;;EAFgBU,KAAK,CAACC;;AAmD1B,2CAAa;EACX4G,KAAK,CAACtE,SAAN,GAAkB;IAChB1D,QAAQ,EAAEsB,SAAS,CAAC+D,SAAV,CAAoB,CAAC/D,SAAS,CAACgD,IAAX,EAAiBhD,SAAS,CAACqC,IAA3B,CAApB,CADM;IAEhBsE,SAAS,EAAE,mBAACxH,KAAD,EAAQ0H,QAAR,EAAqB;UAC1B1H,KAAK,CAAC0H,QAAD,CAAL,IAAmB,CAACC,kBAAkB,CAAC3H,KAAK,CAAC0H,QAAD,CAAN,CAA1C,EAA6D;eACpD,IAAIE,KAAJ,yFAAP;;KAJY;IAShBlB,KAAK,EAAE7F,SAAS,CAACiE,IATD;IAUhBvC,QAAQ,EAAE1B,SAAS,CAACC,MAVJ;IAWhBqB,IAAI,EAAEtB,SAAS,CAAC+D,SAAV,CAAoB,CACxB/D,SAAS,CAACgE,MADc,EAExBhE,SAAS,CAACgH,OAAV,CAAkBhH,SAAS,CAACgE,MAA5B,CAFwB,CAApB,CAXU;IAehBpE,MAAM,EAAEI,SAAS,CAACgD,IAfF;IAgBhBuC,SAAS,EAAEvF,SAAS,CAACiE,IAhBL;IAiBhBqB,MAAM,EAAEtF,SAAS,CAACiE;GAjBpB;;EAoBAyC,KAAK,CAACpE,SAAN,CAAgB5B,iBAAhB,GAAoC,YAAW;4CAC7Cf,OAAO,CACL,EACE,KAAKR,KAAL,CAAWT,QAAX,IACA,CAAC4H,eAAe,CAAC,KAAKnH,KAAL,CAAWT,QAAZ,CADhB,IAEA,KAAKS,KAAL,CAAWwH,SAHb,CADK,EAML,gHANK,CAAP;4CASAhH,OAAO,CACL,EACE,KAAKR,KAAL,CAAWT,QAAX,IACA,CAAC4H,eAAe,CAAC,KAAKnH,KAAL,CAAWT,QAAZ,CADhB,IAEA,KAAKS,KAAL,CAAWS,MAHb,CADK,EAML,0GANK,CAAP;4CASAD,OAAO,CACL,EAAE,KAAKR,KAAL,CAAWwH,SAAX,IAAwB,KAAKxH,KAAL,CAAWS,MAArC,CADK,EAEL,2GAFK,CAAP;GAnBF;;EAyBA8G,KAAK,CAACpE,SAAN,CAAgBC,kBAAhB,GAAqC,UAASC,SAAT,EAAoB;4CACvD7C,OAAO,CACL,EAAE,KAAKR,KAAL,CAAWuC,QAAX,IAAuB,CAACc,SAAS,CAACd,QAApC,CADK,EAEL,yKAFK,CAAP;4CAKA/B,OAAO,CACL,EAAE,CAAC,KAAKR,KAAL,CAAWuC,QAAZ,IAAwBc,SAAS,CAACd,QAApC,CADK,EAEL,qKAFK,CAAP;GANF;;;ACtHF,SAASuF,eAAT,CAAyB3F,IAAzB,EAA+B;SACtBA,IAAI,CAAC4F,MAAL,CAAY,CAAZ,MAAmB,GAAnB,GAAyB5F,IAAzB,GAAgC,MAAMA,IAA7C;;;AAGF,SAAS6F,WAAT,CAAqBC,QAArB,EAA+B1F,QAA/B,EAAyC;MACnC,CAAC0F,QAAL,EAAe,OAAO1F,QAAP;sBAGVA,QADL;IAEEL,QAAQ,EAAE4F,eAAe,CAACG,QAAD,CAAf,GAA4B1F,QAAQ,CAACL;;;;AAInD,SAASgG,aAAT,CAAuBD,QAAvB,EAAiC1F,QAAjC,EAA2C;MACrC,CAAC0F,QAAL,EAAe,OAAO1F,QAAP;MAET4F,IAAI,GAAGL,eAAe,CAACG,QAAD,CAA5B;MAEI1F,QAAQ,CAACL,QAAT,CAAkBkG,OAAlB,CAA0BD,IAA1B,MAAoC,CAAxC,EAA2C,OAAO5F,QAAP;sBAGtCA,QADL;IAEEL,QAAQ,EAAEK,QAAQ,CAACL,QAAT,CAAkBmG,MAAlB,CAAyBF,IAAI,CAACG,MAA9B;;;;AAId,SAASC,SAAT,CAAmBhG,QAAnB,EAA6B;SACpB,OAAOA,QAAP,KAAoB,QAApB,GAA+BA,QAA/B,GAA0CiG,UAAU,CAACjG,QAAD,CAA3D;;;AAGF,SAASkG,aAAT,CAAuBC,UAAvB,EAAmC;SAC1B,YAAM;6CACXpE,SAAS,QAAQ,mCAAR,EAA6CoE,UAA7C,CAAT,GAAApE,SAAS,OAAT;GADF;;;AAKF,SAASqE,IAAT,GAAgB;;;;;;;;;IAQVC;;;;;;;;;;;;UAQJC,aAAa,UAAAtG,QAAQ;aAAI,MAAKuG,UAAL,CAAgBvG,QAAhB,EAA0B,MAA1B,CAAJ;;;UACrBwG,gBAAgB,UAAAxG,QAAQ;aAAI,MAAKuG,UAAL,CAAgBvG,QAAhB,EAA0B,SAA1B,CAAJ;;;UACxByG,eAAe;aAAML,IAAN;;;UACfM,cAAc;aAAMN,IAAN;;;;;;;;SAVdG,aAAA,oBAAWvG,QAAX,EAAqB2G,MAArB,EAA6B;sBACa,KAAKlJ,KADlB;2CACnBiI,QADmB;QACnBA,QADmB,qCACR,EADQ;0CACJzG,OADI;QACJA,OADI,oCACM,EADN;IAE3BA,OAAO,CAAC0H,MAAR,GAAiBA,MAAjB;IACA1H,OAAO,CAACe,QAAR,GAAmByF,WAAW,CAACC,QAAD,EAAWrC,cAAc,CAACrD,QAAD,CAAzB,CAA9B;IACAf,OAAO,CAACY,GAAR,GAAcmG,SAAS,CAAC/G,OAAO,CAACe,QAAT,CAAvB;;;SAQF9B,SAAA,kBAAS;uBAC0D,KAAKT,KAD/D;6CACCiI,QADD;QACCA,QADD,sCACY,EADZ;4CACgBzG,OADhB;QACgBA,OADhB,qCAC0B,EAD1B;6CAC8Be,QAD9B;QAC8BA,QAD9B,sCACyC,GADzC;QACiD4G,IADjD;;QAGD3G,OAAO,GAAG;MACd4G,UAAU,EAAE,oBAAAjH,IAAI;eAAI2F,eAAe,CAACG,QAAQ,GAAGM,SAAS,CAACpG,IAAD,CAArB,CAAnB;OADF;MAEd+G,MAAM,EAAE,KAFM;MAGd3G,QAAQ,EAAE2F,aAAa,CAACD,QAAD,EAAWrC,cAAc,CAACrD,QAAD,CAAzB,CAHT;MAId1D,IAAI,EAAE,KAAKgK,UAJG;MAKdlD,OAAO,EAAE,KAAKoD,aALA;MAMdM,EAAE,EAAEZ,aAAa,CAAC,IAAD,CANH;MAOda,MAAM,EAAEb,aAAa,CAAC,QAAD,CAPP;MAQdc,SAAS,EAAEd,aAAa,CAAC,WAAD,CARV;MASd5F,MAAM,EAAE,KAAKmG,YATC;MAUdxE,KAAK,EAAE,KAAKyE;KAVd;wBAaO,oBAAC,MAAD,eAAYE,IAAZ;MAAkB,OAAO,EAAE3G,OAA3B;MAAoC,aAAa,EAAEhB;OAA1D;;;;EA7BuBd,KAAK,CAACC;;AAiCjC,2CAAa;EACXiI,YAAY,CAAC3F,SAAb,GAAyB;IACvBgF,QAAQ,EAAEpH,SAAS,CAACgE,MADG;IAEvBrD,OAAO,EAAEX,SAAS,CAACC,MAFI;IAGvByB,QAAQ,EAAE1B,SAAS,CAAC+D,SAAV,CAAoB,CAAC/D,SAAS,CAACgE,MAAX,EAAmBhE,SAAS,CAACC,MAA7B,CAApB;GAHZ;;EAMA8H,YAAY,CAACzF,SAAb,CAAuB5B,iBAAvB,GAA2C,YAAW;4CACpDf,OAAO,CACL,CAAC,KAAKR,KAAL,CAAWwC,OADP,EAEL,uEACE,yEAHG,CAAP;GADF;;;ACpFF;;;;IAGMgH;;;;;;;;;SACJ/I,SAAA,kBAAS;;;wBAEL,oBAACqC,OAAD,CAAe,QAAf,QACG,UAAAtB,OAAO,EAAI;OACAA,OAAV,2CAAA8C,SAAS,QAAU,gDAAV,CAAT,GAAAA,SAAS,OAAT;UAEM/B,QAAQ,GAAG,KAAI,CAACvC,KAAL,CAAWuC,QAAX,IAAuBf,OAAO,CAACe,QAAhD;UAEIkH,OAAJ,EAAa1G,KAAb,CALU;;;;;MAWVrC,KAAK,CAAC0G,QAAN,CAAe/H,OAAf,CAAuB,KAAI,CAACW,KAAL,CAAWT,QAAlC,EAA4C,UAAAmK,KAAK,EAAI;YAC/C3G,KAAK,IAAI,IAAT,iBAAiBrC,KAAK,CAACiJ,cAAN,CAAqBD,KAArB,CAArB,EAAkD;UAChDD,OAAO,GAAGC,KAAV;cAEMvH,IAAI,GAAGuH,KAAK,CAAC1J,KAAN,CAAYmC,IAAZ,IAAoBuH,KAAK,CAAC1J,KAAN,CAAY+F,IAA7C;UAEAhD,KAAK,GAAGZ,IAAI,GACRsE,SAAS,CAAClE,QAAQ,CAACL,QAAV,eAAyBwH,KAAK,CAAC1J,KAA/B;YAAsCmC,IAAI,EAAJA;aADvC,GAERX,OAAO,CAACuB,KAFZ;;OANJ;aAYOA,KAAK,gBACRrC,KAAK,CAACkJ,YAAN,CAAmBH,OAAnB,EAA4B;QAAElH,QAAQ,EAARA,QAAF;QAAYkD,aAAa,EAAE1C;OAAvD,CADQ,GAER,IAFJ;KAxBJ,CADF;;;;EAFiBrC,KAAK,CAACC;;AAoC3B,2CAAa;EACX6I,MAAM,CAACvG,SAAP,GAAmB;IACjB1D,QAAQ,EAAEsB,SAAS,CAACqC,IADH;IAEjBX,QAAQ,EAAE1B,SAAS,CAACC;GAFtB;;EAKA0I,MAAM,CAACrG,SAAP,CAAiBC,kBAAjB,GAAsC,UAASC,SAAT,EAAoB;4CACxD7C,OAAO,CACL,EAAE,KAAKR,KAAL,CAAWuC,QAAX,IAAuB,CAACc,SAAS,CAACd,QAApC,CADK,EAEL,0KAFK,CAAP;4CAKA/B,OAAO,CACL,EAAE,CAAC,KAAKR,KAAL,CAAWuC,QAAZ,IAAwBc,SAAS,CAACd,QAApC,CADK,EAEL,sKAFK,CAAP;GANF;;;AC9CF;;;;AAGA,SAASsH,UAAT,CAAoBlJ,SAApB,EAA+B;MACvBmB,WAAW,oBAAiBnB,SAAS,CAACmB,WAAV,IAAyBnB,SAAS,CAACkB,IAApD,OAAjB;;MACMiI,CAAC,GAAG,SAAJA,CAAI,CAAA9J,KAAK,EAAI;QACT+J,mBADS,GACkC/J,KADlC,CACT+J,mBADS;QACeC,cADf,iCACkChK,KADlC;;wBAIf,oBAAC8C,OAAD,CAAe,QAAf,QACG,UAAAtB,OAAO,EAAI;OAERA,OADF,2CAAA8C,SAAS,iCAEgBxC,WAFhB,4BAAT,GAAAwC,SAAS,OAAT;0BAKE,oBAAC,SAAD,eACM0F,cADN,EAEMxI,OAFN;QAGE,GAAG,EAAEuI;SAJT;KANJ,CADF;GAHF;;EAsBAD,CAAC,CAAChI,WAAF,GAAgBA,WAAhB;EACAgI,CAAC,CAACG,gBAAF,GAAqBtJ,SAArB;;6CAEa;IACXmJ,CAAC,CAAC7G,SAAF,GAAc;MACZ8G,mBAAmB,EAAElJ,SAAS,CAAC+D,SAAV,CAAoB,CACvC/D,SAAS,CAACgE,MAD6B,EAEvChE,SAAS,CAACgD,IAF6B,EAGvChD,SAAS,CAACC,MAH6B,CAApB;KADvB;;;SASKoJ,YAAY,CAACJ,CAAD,EAAInJ,SAAJ,CAAnB;;;ACxCF,IAAMwJ,UAAU,GAAGzJ,KAAK,CAACyJ,UAAzB;AAEA,AAAO,SAASC,UAAT,GAAsB;6CACd;MAET,OAAOD,UAAP,KAAsB,UADxB,4CAAA7F,SAAS,QAEP,yDAFO,CAAT,GAAAA,SAAS,OAAT;;;SAMK6F,UAAU,CAACnH,cAAD,CAAjB;;AAGF,AAAO,SAASqH,WAAT,GAAuB;6CACf;MAET,OAAOF,UAAP,KAAsB,UADxB,4CAAA7F,SAAS,QAEP,0DAFO,CAAT,GAAAA,SAAS,OAAT;;;SAMK6F,UAAU,CAACrH,OAAD,CAAV,CAA0BP,QAAjC;;AAGF,AAAO,SAAS+H,SAAT,GAAqB;6CACb;MAET,OAAOH,UAAP,KAAsB,UADxB,4CAAA7F,SAAS,QAEP,wDAFO,CAAT,GAAAA,SAAS,OAAT;;;MAMIvB,KAAK,GAAGoH,UAAU,CAACrH,OAAD,CAAV,CAA0BC,KAAxC;SACOA,KAAK,GAAGA,KAAK,CAACV,MAAT,GAAkB,EAA9B;;AAGF,AAAO,SAASkI,aAAT,CAAuBpI,IAAvB,EAA6B;6CACrB;MAET,OAAOgI,UAAP,KAAsB,UADxB,4CAAA7F,SAAS,QAEP,4DAFO,CAAT,GAAAA,SAAS,OAAT;;;MAMI/B,QAAQ,GAAG8H,WAAW,EAA5B;MACMtH,KAAK,GAAGoH,UAAU,CAACrH,OAAD,CAAV,CAA0BC,KAAxC;SACOZ,IAAI,GAAGsE,SAAS,CAAClE,QAAQ,CAACL,QAAV,EAAoBC,IAApB,CAAZ,GAAwCY,KAAnD;;;ACrDF,2CAAa;MACP,OAAO9E,MAAP,KAAkB,WAAtB,EAAmC;QAC3BC,QAAM,GAAGD,MAAf;QACMG,GAAG,GAAG,wBAAZ;QACMoM,UAAU,GAAG;MAAEC,GAAG,EAAE,UAAP;MAAmBC,GAAG,EAAE,YAAxB;MAAsCC,GAAG,EAAE;KAA9D;;QAEIzM,QAAM,CAACE,GAAD,CAAN,IAAeF,QAAM,CAACE,GAAD,CAAN,KAAgBiC,KAAnC,EAA6D;UACrDuK,gBAAgB,GAAGJ,UAAU,CAACtM,QAAM,CAACE,GAAD,CAAP,CAAnC;UACMyM,kBAAkB,GAAGL,UAAU,CAACnK,KAAD,CAArC,CAF2D;;;YAMrD,IAAIuH,KAAJ,CACJ,yBAAuBiD,kBAAvB,2EAC2CD,gBAD3C,8CADI,CAAN;;;IAOF1M,QAAM,CAACE,GAAD,CAAN,GAAciC,KAAd;;;;;;"}